# Field Mapping System Guide

This guide explains how to use the enhanced field mapping system for the Church Bulletin Generator. The new system makes it easy to manage and update all template fields, ensuring that dates, times, names, and other dynamic content are properly replaced when generating bulletins.

## Overview

The field mapping system consists of several components:

1. **Field Mapper** - Core engine that handles field replacements
2. **Field Configuration Manager** - Manages configuration files
3. **Template Parser** - Enhanced to detect fields automatically
4. **CLI Commands** - Easy-to-use commands for field management

## Quick Start

### 1. Analyze Your Template

First, analyze your template to see what fields can be updated:

```bash
python main.py fields analyze
```

This will show you:
- All detected fields in your template
- Which fields are already mapped
- Which fields need mapping
- Which configured fields are missing from the template

### 2. Auto-Update Field Mappings

To automatically add mappings for detected fields:

```bash
python main.py fields auto-update
```

This will:
- Detect new fields in your template
- Create suggested mappings for them
- Add them to your configuration
- Create a backup of your existing configuration

### 3. Validate Your Mappings

Check that your field mappings are working correctly:

```bash
python main.py fields validate
```

### 4. Generate a Bulletin

Generate a bulletin as usual, and the new field mapping system will be used automatically:

```bash
python main.py generate --date 2025-08-17
```

## Field Configuration File

The field mappings are stored in `field_mappings.json`. This file defines what text should be replaced and how.

### Configuration Structure

```json
{
  "date_fields": {
    "10-Aug": {
      "type": "date",
      "format": "short",
      "source": "bulletin_date",
      "description": "Main bulletin date in short format"
    }
  },
  "time_fields": {
    "7.45am": {
      "type": "time",
      "location": "beachmere",
      "description": "Beachmere service time"
    }
  },
  "people_fields": {
    "Rev. Jason Grimsey": {
      "type": "person",
      "role": "minister",
      "field": "name",
      "description": "Minister name"
    }
  }
}
```

### Field Types

#### Date Fields
- **type**: "date"
- **format**: Date format type (short, full_with_year, ordinal_month, etc.)
- **source**: Date source (bulletin_date, next_sunday, prev_sunday)

#### Time Fields
- **type**: "time"
- **location**: Service location (beachmere, caboolture, upper_caboolture, tongan_service)

#### People Fields
- **type**: "person"
- **role**: Person's role (minister, pastor, admin)
- **field**: Which field to use (name, phone, email)

#### Year Fields
- **type**: "year"
- **source**: "current_year" (updates to the current year)

## CLI Commands

### Field Management Commands

All field management commands are under the `fields` group:

```bash
python main.py fields --help
```

#### Analyze Template
```bash
python main.py fields analyze [--config custom_config.json]
```
Analyzes the template to detect all replaceable fields.

#### Validate Mappings
```bash
python main.py fields validate [--config custom_config.json]
```
Validates that field mappings are correct and complete.

#### Auto-Update Mappings
```bash
python main.py fields auto-update [--config custom_config.json] [--no-backup]
```
Automatically adds mappings for newly detected fields.

#### List All Mappings
```bash
python main.py fields list-mappings [--config custom_config.json]
```
Lists all configured field mappings.

#### Export Template
```bash
python main.py fields export-template [--output template.json]
```
Exports a template configuration file for easy editing.

## Custom Configuration Files

You can use custom configuration files for different templates or scenarios:

```bash
# Use a custom config file
python main.py fields analyze --config my_custom_mappings.json

# Generate bulletin with custom config
python main.py generate --date 2025-08-17 --config my_custom_mappings.json
```

## Testing and Validation

### Test the Field Mapping System
```bash
python test_field_mappings.py
```
Runs comprehensive tests on the field mapping system.

### Validate Bulletin Generation
```bash
python validate_bulletin_fields.py
```
Validates that bulletin generation produces correct output for multiple dates.

## Adding New Fields

### Method 1: Automatic Detection
1. Add the new text to your template
2. Run `python main.py fields analyze` to detect it
3. Run `python main.py fields auto-update` to add mapping

### Method 2: Manual Configuration
1. Edit `field_mappings.json`
2. Add your field under the appropriate category
3. Run `python main.py fields validate` to check

Example of manually adding a date field:
```json
{
  "date_fields": {
    "Sunday, 17th August": {
      "type": "date",
      "format": "full_ordinal",
      "source": "bulletin_date",
      "description": "Full date with ordinal day"
    }
  }
}
```

## Troubleshooting

### Field Not Being Updated
1. Check if the field is detected: `python main.py fields analyze`
2. Check if it's mapped: `python main.py fields list-mappings`
3. Validate configuration: `python main.py fields validate`
4. Check exact text match (case-sensitive)

### Configuration Errors
1. Validate your config: `python main.py fields validate`
2. Check JSON syntax in your configuration file
3. Ensure required fields are present

### Template Changes
When you modify your template:
1. Run `python main.py fields analyze` to detect changes
2. Run `python main.py fields auto-update` to add new mappings
3. Remove obsolete mappings manually if needed

## Best Practices

1. **Always backup** your configuration before making changes
2. **Test thoroughly** after adding new fields
3. **Use descriptive names** in field descriptions
4. **Validate regularly** to catch issues early
5. **Keep configurations organized** by using custom config files for different templates

## Advanced Usage

### Custom Date Formats
You can add custom date formats by extending the field mapper:

```python
# In your custom script
from bulletin_generator.core.field_mapper import FieldMapper

mapper = FieldMapper()
mapper.add_field_mapping("date_fields", "Custom Date Text", {
    "type": "date",
    "format": "custom",
    "source": "bulletin_date",
    "description": "Custom date format"
})
```

### Batch Operations
For bulk updates, you can script operations:

```python
from bulletin_generator.utils.field_config_manager import FieldConfigManager

config_manager = FieldConfigManager()

# Add multiple fields
fields_to_add = {
    "New Date 1": {"type": "date", "format": "short", "source": "bulletin_date"},
    "New Date 2": {"type": "date", "format": "full", "source": "next_sunday"}
}

for field_text, config in fields_to_add.items():
    config_manager.add_field_mapping("date_fields", field_text, config)

config_manager.save_config()
```

## Support

If you encounter issues or need help:
1. Check this guide first
2. Run the validation tools
3. Check the logs in `bulletin_generator.log`
4. Review the configuration file syntax
