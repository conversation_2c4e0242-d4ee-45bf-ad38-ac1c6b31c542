"""
Validation utility functions
"""

import re
from datetime import date
from typing import Optional

def validate_date(date_string: str) -> bool:
    """Validate date string format"""
    from .date_utils import parse_date
    return parse_date(date_string) is not None

def validate_email(email: str) -> bool:
    """Validate email address format"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_phone(phone: str) -> bool:
    """Validate Australian phone number format"""
    if not phone:
        return False
    
    # Remove spaces, dashes, and parentheses
    clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # Australian phone patterns
    patterns = [
        r'^04\d{8}$',      # Mobile: 04xxxxxxxx
        r'^0[2-8]\d{8}$',  # Landline: 0xxxxxxxxx
        r'^\+614\d{8}$',   # International mobile: +614xxxxxxxx
        r'^\+61[2-8]\d{8}$' # International landline: +61xxxxxxxxx
    ]
    
    return any(re.match(pattern, clean_phone) for pattern in patterns)

def validate_time(time_string: str) -> bool:
    """Validate time format (e.g., 9.00am, 10:30pm)"""
    if not time_string:
        return False
    
    patterns = [
        r'^\d{1,2}\.\d{2}[ap]m$',  # 9.00am, 10.30pm
        r'^\d{1,2}:\d{2}[ap]m$',   # 9:00am, 10:30pm
        r'^\d{1,2}[ap]m$',         # 9am, 10pm
    ]
    
    return any(re.match(pattern, time_string.lower()) for pattern in patterns)
