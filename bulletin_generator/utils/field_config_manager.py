"""
Field Configuration Manager

Utility for managing field mapping configurations, including loading, saving,
and validating field mapping configurations from JSON files.
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)


class FieldConfigManager:
    """Manages field mapping configuration files"""
    
    def __init__(self, config_path: Optional[Path] = None):
        """Initialize with optional custom config path"""
        self.config_path = config_path or Path("field_mappings.json")
        self.config = {}
        
        if self.config_path.exists():
            self.load_config()
    
    def load_config(self, config_path: Optional[Path] = None) -> Dict[str, Any]:
        """Load field mapping configuration from JSON file"""
        path = config_path or self.config_path
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            logger.info(f"Loaded field configuration from {path}")
            return self.config
            
        except FileNotFoundError:
            logger.warning(f"Configuration file not found: {path}")
            self.config = self._get_default_config()
            return self.config
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            raise
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
    
    def save_config(self, config_path: Optional[Path] = None) -> None:
        """Save current configuration to JSON file"""
        path = config_path or self.config_path
        
        try:
            # Update last_updated timestamp
            self.config["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved field configuration to {path}")
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            raise
    
    def add_field_mapping(self, category: str, field_text: str, field_config: Dict[str, Any]) -> None:
        """Add a new field mapping to the configuration"""
        if category not in self.config:
            self.config[category] = {}
        
        self.config[category][field_text] = field_config
        logger.info(f"Added field mapping: {category}.{field_text}")
    
    def remove_field_mapping(self, category: str, field_text: str) -> bool:
        """Remove a field mapping from the configuration"""
        if category in self.config and field_text in self.config[category]:
            del self.config[category][field_text]
            logger.info(f"Removed field mapping: {category}.{field_text}")
            return True
        return False
    
    def update_field_mapping(self, category: str, field_text: str, field_config: Dict[str, Any]) -> bool:
        """Update an existing field mapping"""
        if category in self.config and field_text in self.config[category]:
            self.config[category][field_text].update(field_config)
            logger.info(f"Updated field mapping: {category}.{field_text}")
            return True
        return False
    
    def get_field_mapping(self, category: str, field_text: str) -> Optional[Dict[str, Any]]:
        """Get a specific field mapping"""
        return self.config.get(category, {}).get(field_text)
    
    def list_categories(self) -> List[str]:
        """List all field mapping categories"""
        return [cat for cat in self.config.keys() if isinstance(self.config[cat], dict) and cat.endswith('_fields')]
    
    def list_fields_in_category(self, category: str) -> List[str]:
        """List all fields in a specific category"""
        return list(self.config.get(category, {}).keys())
    
    def get_all_field_mappings(self) -> Dict[str, Dict[str, Any]]:
        """Get all field mappings organized by category"""
        field_categories = self.list_categories()
        return {cat: self.config.get(cat, {}) for cat in field_categories}
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """Validate the current configuration
        
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        errors = []
        
        # Check required top-level keys
        required_keys = ["date_fields", "time_fields", "people_fields"]
        for key in required_keys:
            if key not in self.config:
                errors.append(f"Missing required category: {key}")
        
        # Validate field mappings structure
        for category, fields in self.get_all_field_mappings().items():
            if not isinstance(fields, dict):
                errors.append(f"Category {category} should be a dictionary")
                continue
            
            for field_text, field_config in fields.items():
                if not isinstance(field_config, dict):
                    errors.append(f"Field config for {category}.{field_text} should be a dictionary")
                    continue
                
                # Check required field properties
                if "type" not in field_config:
                    errors.append(f"Missing 'type' in {category}.{field_text}")
                
                if "description" not in field_config:
                    errors.append(f"Missing 'description' in {category}.{field_text}")
        
        # Validate required fields exist
        validation_rules = self.config.get("validation_rules", {})
        required_fields = validation_rules.get("required_fields", [])
        
        all_fields = []
        for fields in self.get_all_field_mappings().values():
            all_fields.extend(fields.keys())
        
        for required_field in required_fields:
            if required_field not in all_fields:
                errors.append(f"Required field missing from configuration: {required_field}")
        
        return len(errors) == 0, errors
    
    def export_template(self, output_path: Path) -> None:
        """Export a template configuration file for easy editing"""
        template = {
            "description": "Field mapping configuration template",
            "version": "1.0",
            "last_updated": datetime.now().strftime("%Y-%m-%d"),
            
            "date_fields": {
                "EXAMPLE-DATE": {
                    "type": "date",
                    "format": "short|full_with_year|ordinal_month|custom",
                    "source": "bulletin_date|next_sunday|prev_sunday",
                    "description": "Description of what this field represents"
                }
            },
            
            "time_fields": {
                "EXAMPLE-TIME": {
                    "type": "time",
                    "location": "beachmere|caboolture|upper_caboolture|tongan_service",
                    "description": "Description of what this time represents"
                }
            },
            
            "people_fields": {
                "EXAMPLE-NAME": {
                    "type": "person",
                    "role": "minister|pastor|admin",
                    "field": "name|phone|email",
                    "description": "Description of what this person field represents"
                }
            },
            
            "year_fields": {
                "YYYY": {
                    "type": "year",
                    "source": "current_year",
                    "description": "Year that should be updated to current year"
                }
            },
            
            "settings": {
                "auto_detect_new_fields": True,
                "case_sensitive_matching": False,
                "backup_before_replace": True,
                "validate_after_replace": True
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(template, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Exported configuration template to {output_path}")
    
    def merge_detected_fields(self, detected_fields: Dict[str, List[str]]) -> Dict[str, Dict[str, Any]]:
        """Merge detected fields with existing configuration, suggesting new mappings"""
        suggestions = {}
        
        # Get existing field texts
        existing_fields = set()
        for fields in self.get_all_field_mappings().values():
            existing_fields.update(fields.keys())
        
        # Process detected fields and suggest mappings for new ones
        for category, field_list in detected_fields.items():
            category_key = f"{category}_fields"
            suggestions[category_key] = {}
            
            for field_text in field_list:
                if field_text not in existing_fields:
                    # Suggest a mapping based on the category
                    if category == "dates":
                        suggestions[category_key][field_text] = {
                            "type": "date",
                            "format": "auto_detect",
                            "source": "bulletin_date",
                            "description": f"Auto-detected date field: {field_text}"
                        }
                    elif category == "times":
                        suggestions[category_key][field_text] = {
                            "type": "time",
                            "location": "auto_detect",
                            "description": f"Auto-detected time field: {field_text}"
                        }
                    elif category == "names":
                        suggestions[category_key][field_text] = {
                            "type": "person",
                            "role": "auto_detect",
                            "field": "name",
                            "description": f"Auto-detected name field: {field_text}"
                        }
                    elif category == "years":
                        suggestions[category_key][field_text] = {
                            "type": "year",
                            "source": "current_year",
                            "description": f"Auto-detected year field: {field_text}"
                        }
        
        return suggestions
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration when no config file exists"""
        return {
            "description": "Default field mapping configuration",
            "version": "1.0",
            "last_updated": datetime.now().strftime("%Y-%m-%d"),
            "date_fields": {},
            "time_fields": {},
            "people_fields": {},
            "year_fields": {},
            "settings": {
                "auto_detect_new_fields": True,
                "case_sensitive_matching": False,
                "backup_before_replace": True,
                "validate_after_replace": True
            }
        }
