"""
File utility functions
"""

import shutil
from pathlib import Path
from typing import Optional

def ensure_directory(path: Path) -> None:
    """Ensure directory exists, create if it doesn't"""
    path.mkdir(parents=True, exist_ok=True)

def copy_template(source: Path, destination: Path) -> None:
    """Copy template file to destination"""
    ensure_directory(destination.parent)
    shutil.copy2(source, destination)

def get_safe_filename(filename: str) -> str:
    """Get safe filename by removing/replacing invalid characters"""
    # Replace invalid characters with underscores
    invalid_chars = '<>:"/\\|?*'
    safe_name = filename
    for char in invalid_chars:
        safe_name = safe_name.replace(char, '_')
    
    # Remove multiple consecutive underscores
    while '__' in safe_name:
        safe_name = safe_name.replace('__', '_')
    
    # Remove leading/trailing underscores
    safe_name = safe_name.strip('_')
    
    return safe_name

def backup_file(file_path: Path, backup_suffix: str = '.bak') -> Optional[Path]:
    """Create backup of file"""
    if not file_path.exists():
        return None
    
    backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
    shutil.copy2(file_path, backup_path)
    return backup_path
