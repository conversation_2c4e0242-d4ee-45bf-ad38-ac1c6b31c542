"""
Bulletin data model - aggregates all bulletin content
"""

from dataclasses import dataclass, field
from datetime import date
from typing import List, Optional, Dict, Any

from .service import ServiceTime
from .event import Event
from .bible_verse import BibleVerse
from .contact import Contact

@dataclass
class BulletinData:
    """Aggregated data for a complete bulletin"""
    date: date
    title: Optional[str] = None
    edition_number: Optional[int] = None
    theme: Optional[str] = None
    
    # Service information
    service_times: List[ServiceTime] = field(default_factory=list)
    
    # Content
    bible_verse: Optional[BibleVerse] = None
    events: List[Event] = field(default_factory=list)
    
    # Contact information
    minister: Optional[Contact] = None
    office_contact: Optional[Contact] = None
    
    # Additional metadata
    special_notes: Optional[str] = None
    template_version: str = 'standard'
    
    def get_service_by_location(self, location: str) -> Optional[ServiceTime]:
        """Get service time for a specific location"""
        for service in self.service_times:
            if service.location.lower() == location.lower():
                return service
        return None
    
    def get_events_by_category(self, category: str) -> List[Event]:
        """Get events filtered by category"""
        return [event for event in self.events if event.category.lower() == category.lower()]
    
    def get_high_priority_events(self) -> List[Event]:
        """Get high priority events (priority >= 3)"""
        return [event for event in self.events if event.priority >= 3]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for template rendering"""
        return {
            'date': self.date.isoformat(),
            'formatted_date': self.date.strftime('%d-%b'),  # e.g., "10-Aug"
            'full_date': self.date.strftime('%A, %d %B %Y'),  # e.g., "Sunday, 10 August 2025"
            'title': self.title,
            'edition_number': self.edition_number,
            'theme': self.theme,
            'service_times': [service.to_dict() for service in self.service_times],
            'bible_verse': self.bible_verse.to_dict() if self.bible_verse else None,
            'events': [event.to_dict() for event in self.events],
            'minister': self.minister.to_dict() if self.minister else None,
            'office_contact': self.office_contact.to_dict() if self.office_contact else None,
            'special_notes': self.special_notes,
            'template_version': self.template_version
        }
    
    def get_template_variables(self) -> Dict[str, str]:
        """Get variables for template replacement"""
        variables = {
            'bulletin_date': self.date.strftime('%d-%b'),
            'service_date': self.date.strftime('%d-%b'),
            'full_date': self.date.strftime('%A, %d %B %Y'),
        }
        
        # Add service times
        for service in self.service_times:
            location_key = service.location.lower().replace(' ', '_')
            variables[f'{location_key}_time'] = service.time
            if service.preacher:
                variables[f'{location_key}_preacher'] = service.preacher
        
        # Add minister information
        if self.minister:
            variables['minister_name'] = self.minister.name
            variables['minister_phone'] = self.minister.phone or ''
            variables['minister_email'] = self.minister.email or ''
        
        # Add bible verse
        if self.bible_verse:
            variables['bible_verse'] = self.bible_verse.get_formatted_verse()
            variables['bible_reference'] = self.bible_verse.verse_reference
        
        # Add theme
        if self.theme:
            variables['weekly_theme'] = self.theme
        
        return variables
    
    def __str__(self) -> str:
        """String representation"""
        return f"Bulletin for {self.date.strftime('%A, %d %B %Y')}"
