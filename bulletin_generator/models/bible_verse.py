"""
Bible Verse data model
"""

from dataclasses import dataclass
from datetime import date
from typing import Optional
import sqlite3

@dataclass
class BibleVerse:
    """Represents a Bible verse for a specific date"""
    id: Optional[int]
    date: date
    verse_reference: str
    verse_text: Optional[str] = None
    theme: Optional[str] = None
    
    @classmethod
    def from_db_row(cls, row: sqlite3.Row) -> 'BibleVerse':
        """Create BibleVerse from database row"""
        return cls(
            id=row['id'],
            date=date.fromisoformat(row['date']),
            verse_reference=row['verse_reference'],
            verse_text=row['verse_text'],
            theme=row['theme']
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'date': self.date.isoformat(),
            'verse_reference': self.verse_reference,
            'verse_text': self.verse_text,
            'theme': self.theme
        }
    
    def get_formatted_verse(self) -> str:
        """Get formatted verse with reference"""
        if self.verse_text:
            return f'"{self.verse_text}" - {self.verse_reference}'
        else:
            return self.verse_reference
    
    def __str__(self) -> str:
        """String representation"""
        return f"{self.verse_reference} ({self.date})"
