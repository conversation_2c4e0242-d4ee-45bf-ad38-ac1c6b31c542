"""
Contact data model
"""

from dataclasses import dataclass
from typing import Optional
import sqlite3

@dataclass
class Contact:
    """Represents a church contact person"""
    id: Optional[int]
    name: str
    role: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    availability_notes: Optional[str] = None
    active: bool = True
    
    @classmethod
    def from_db_row(cls, row: sqlite3.Row) -> 'Contact':
        """Create Contact from database row"""
        return cls(
            id=row['id'],
            name=row['name'],
            role=row['role'],
            phone=row['phone'],
            email=row['email'],
            availability_notes=row['availability_notes'],
            active=bool(row['active'])
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'id': self.id,
            'name': self.name,
            'role': self.role,
            'phone': self.phone,
            'email': self.email,
            'availability_notes': self.availability_notes,
            'active': self.active
        }
    
    def get_formatted_contact(self) -> str:
        """Get formatted contact information"""
        parts = [self.name]
        if self.role:
            parts.append(f"({self.role})")
        if self.phone:
            parts.append(f"Ph: {self.phone}")
        if self.email:
            parts.append(f"Email: {self.email}")
        return " | ".join(parts)
    
    def __str__(self) -> str:
        """String representation"""
        role_str = f" - {self.role}" if self.role else ""
        return f"{self.name}{role_str}"
