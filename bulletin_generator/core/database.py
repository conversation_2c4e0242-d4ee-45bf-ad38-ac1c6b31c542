"""
Database operations for the Church Bulletin Generator
"""

import sqlite3
import logging
from datetime import date, datetime
from pathlib import Path
from typing import List, Optional, Dict, Any
from contextlib import contextmanager

from ..config import get_config
from ..models.service import ServiceTime
from ..models.event import Event
from ..models.bible_verse import BibleVerse
from ..models.contact import Contact

logger = logging.getLogger(__name__)

class BulletinDatabase:
    """Database manager for bulletin data"""
    
    def __init__(self, db_path: Optional[Path] = None):
        """Initialize database connection"""
        config = get_config()
        self.db_path = db_path or config['paths']['database_path']
        self.schema_path = config['paths']['database_schema_path']
        self._ensure_database_exists()
    
    def _ensure_database_exists(self):
        """Ensure database file and directory exist"""
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        if not self.db_path.exists():
            logger.info(f"Creating new database at {self.db_path}")
            self.initialize_database()
    
    @contextmanager
    def get_connection(self):
        """Get database connection with automatic cleanup"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def initialize_database(self):
        """Initialize database with schema"""
        try:
            with open(self.schema_path, 'r') as f:
                schema_sql = f.read()
            
            with self.get_connection() as conn:
                conn.executescript(schema_sql)
                conn.commit()
                logger.info("Database initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def check_connection(self) -> bool:
        """Check if database connection is working"""
        try:
            with self.get_connection() as conn:
                conn.execute("SELECT 1")
                return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    # Service Times
    def add_service_time(self, date: date, location: str, time: str, 
                        preacher: Optional[str] = None, service_type: str = 'Regular') -> int:
        """Add a service time entry"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """INSERT INTO service_times (date, location, time, preacher, service_type)
                   VALUES (?, ?, ?, ?, ?)""",
                (date, location, time, preacher, service_type)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_service_times(self, date: date) -> List[ServiceTime]:
        """Get service times for a specific date"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """SELECT * FROM service_times WHERE date = ? ORDER BY time""",
                (date,)
            )
            rows = cursor.fetchall()
            return [ServiceTime.from_db_row(row) for row in rows]
    
    # Events
    def add_event(self, date: date, title: str, description: Optional[str] = None,
                 event_date: Optional[date] = None, time: Optional[str] = None,
                 location: Optional[str] = None, contact_person: Optional[str] = None,
                 category: str = 'General', priority: int = 1) -> int:
        """Add an event entry"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """INSERT INTO events (date, event_date, title, description, time, 
                   location, contact_person, category, priority)
                   VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                (date, event_date or date, title, description, time, 
                 location, contact_person, category, priority)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_events(self, date: Optional[date] = None) -> List[Event]:
        """Get events for a specific date or all events"""
        with self.get_connection() as conn:
            if date:
                cursor = conn.execute(
                    """SELECT * FROM events WHERE date = ? ORDER BY priority DESC, time""",
                    (date,)
                )
            else:
                cursor = conn.execute(
                    """SELECT * FROM events ORDER BY date DESC, priority DESC, time"""
                )
            rows = cursor.fetchall()
            return [Event.from_db_row(row) for row in rows]
    
    # Bible Verses
    def add_bible_verse(self, date: date, verse_reference: str, 
                       verse_text: Optional[str] = None, theme: Optional[str] = None) -> int:
        """Add a bible verse entry"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """INSERT INTO bible_verses (date, verse_reference, verse_text, theme)
                   VALUES (?, ?, ?, ?)""",
                (date, verse_reference, verse_text, theme)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_bible_verse(self, date: date) -> Optional[BibleVerse]:
        """Get bible verse for a specific date"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """SELECT * FROM bible_verses WHERE date = ?""",
                (date,)
            )
            row = cursor.fetchone()
            return BibleVerse.from_db_row(row) if row else None
    
    # Contacts
    def add_contact(self, name: str, role: Optional[str] = None, 
                   phone: Optional[str] = None, email: Optional[str] = None,
                   availability_notes: Optional[str] = None) -> int:
        """Add a contact entry"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """INSERT INTO contacts (name, role, phone, email, availability_notes)
                   VALUES (?, ?, ?, ?, ?)""",
                (name, role, phone, email, availability_notes)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_contacts(self, active_only: bool = True) -> List[Contact]:
        """Get all contacts"""
        with self.get_connection() as conn:
            if active_only:
                cursor = conn.execute(
                    """SELECT * FROM contacts WHERE active = 1 ORDER BY name"""
                )
            else:
                cursor = conn.execute(
                    """SELECT * FROM contacts ORDER BY name"""
                )
            rows = cursor.fetchall()
            return [Contact.from_db_row(row) for row in rows]
    
    def get_contact_by_role(self, role: str) -> Optional[Contact]:
        """Get contact by role (e.g., 'Minister')"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """SELECT * FROM contacts WHERE role = ? AND active = 1 LIMIT 1""",
                (role,)
            )
            row = cursor.fetchone()
            return Contact.from_db_row(row) if row else None
    
    # Bulletin Metadata
    def add_bulletin_metadata(self, date: date, title: Optional[str] = None,
                             edition_number: Optional[int] = None, 
                             theme: Optional[str] = None) -> int:
        """Add bulletin metadata"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """INSERT OR REPLACE INTO bulletin_metadata 
                   (date, title, edition_number, theme)
                   VALUES (?, ?, ?, ?)""",
                (date, title, edition_number, theme)
            )
            conn.commit()
            return cursor.lastrowid
    
    def get_bulletin_metadata(self, date: date) -> Optional[Dict[str, Any]]:
        """Get bulletin metadata for a specific date"""
        with self.get_connection() as conn:
            cursor = conn.execute(
                """SELECT * FROM bulletin_metadata WHERE date = ?""",
                (date,)
            )
            row = cursor.fetchone()
            return dict(row) if row else None
