"""
Field Mapper - Manages template field mappings and replacements

This module provides a comprehensive system for managing all template field
replacements, making it easy to configure what fields get updated and how.
"""

import json
import re
from datetime import date, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

from ..config import get_config
from ..utils.date_utils import format_date

logger = logging.getLogger(__name__)


class FieldMapper:
    """Manages template field mappings and generates replacement values"""
    
    def __init__(self, config_path: Optional[Path] = None):
        """Initialize field mapper with configuration"""
        self.config = get_config()
        self.field_mappings = self.config.get('template_field_mappings', {})
        
        # Load custom configuration if provided
        if config_path and config_path.exists():
            self.load_custom_mappings(config_path)
    
    def load_custom_mappings(self, config_path: Path):
        """Load custom field mappings from JSON file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                custom_mappings = json.load(f)
            
            # Merge custom mappings with defaults
            for category, fields in custom_mappings.items():
                if category in self.field_mappings:
                    self.field_mappings[category].update(fields)
                else:
                    self.field_mappings[category] = fields
                    
            logger.info(f"Loaded custom field mappings from {config_path}")
        except Exception as e:
            logger.error(f"Failed to load custom mappings: {e}")
    
    def save_custom_mappings(self, config_path: Path):
        """Save current field mappings to JSON file"""
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.field_mappings, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved field mappings to {config_path}")
        except Exception as e:
            logger.error(f"Failed to save mappings: {e}")
    
    def get_date_mappings(self, bulletin_date: date) -> Dict[str, str]:
        """Generate date-based field mappings for a bulletin date"""
        mappings = {}
        
        # Calculate related dates
        next_sunday = bulletin_date + timedelta(days=7)
        prev_sunday = bulletin_date - timedelta(days=7)
        current_year = bulletin_date.year
        
        # Helper function for ordinal dates
        def get_ordinal(day: int) -> str:
            if 10 <= day % 100 <= 20:
                suffix = 'th'
            else:
                suffix = {1: 'st', 2: 'nd', 3: 'rd'}.get(day % 10, 'th')
            return f"{day}{suffix}"
        
        # Process date fields
        for old_text, field_config in self.field_mappings.get("date_fields", {}).items():
            source_date = bulletin_date
            if field_config["source"] == "next_sunday":
                source_date = next_sunday
            elif field_config["source"] == "prev_sunday":
                source_date = prev_sunday
            
            # Generate replacement based on format
            format_type = field_config["format"]
            if format_type == "short":
                new_text = source_date.strftime('%d-%b')
            elif format_type == "full_with_year":
                new_text = source_date.strftime('%d %B %Y')
            elif format_type == "ordinal_month":
                new_text = f"{get_ordinal(source_date.day)} {source_date.strftime('%B')}"
            elif format_type == "this_sunday_ordinal":
                new_text = f"this Sunday, {get_ordinal(source_date.day)} {source_date.strftime('%B')}"
            elif format_type == "next_sunday_full":
                new_text = f"next Sunday, {source_date.day} {source_date.strftime('%B %Y')}"
            elif format_type == "future_date_full":
                new_text = f"{get_ordinal(source_date.day)} {source_date.strftime('%B %Y')}"
            elif format_type == "services_later_full":
                new_text = f"services later on {get_ordinal(source_date.day)} {source_date.strftime('%B %Y')}"
            else:
                new_text = source_date.strftime('%d-%b')  # Default format
            
            mappings[old_text] = new_text
        
        # Process year fields
        for old_text, field_config in self.field_mappings.get("year_fields", {}).items():
            mappings[old_text] = str(current_year)
        
        return mappings
    
    def get_service_time_mappings(self, service_times: List[Any]) -> Dict[str, str]:
        """Generate service time field mappings"""
        mappings = {}
        
        # Create lookup for service times by location
        time_lookup = {}
        for service in service_times:
            location_key = service.location.lower().replace(' ', '_')
            time_lookup[location_key] = service.time
        
        # Process time fields
        for old_text, field_config in self.field_mappings.get("time_fields", {}).items():
            location = field_config["location"]
            if location in time_lookup:
                mappings[old_text] = time_lookup[location]
        
        return mappings
    
    def get_people_mappings(self, contacts: Dict[str, Any]) -> Dict[str, str]:
        """Generate people/contact field mappings"""
        mappings = {}
        
        # Process people fields
        for old_text, field_config in self.field_mappings.get("people_fields", {}).items():
            role = field_config["role"]
            field = field_config["field"]
            
            if role in contacts and contacts[role]:
                contact = contacts[role]
                if hasattr(contact, field):
                    mappings[old_text] = getattr(contact, field)
                elif isinstance(contact, dict) and field in contact:
                    mappings[old_text] = contact[field]
        
        return mappings
    
    def get_all_mappings(self, bulletin_date: date, service_times: List[Any] = None, 
                        contacts: Dict[str, Any] = None) -> Dict[str, str]:
        """Get all field mappings for a bulletin"""
        all_mappings = {}
        
        # Date mappings
        all_mappings.update(self.get_date_mappings(bulletin_date))
        
        # Service time mappings
        if service_times:
            all_mappings.update(self.get_service_time_mappings(service_times))
        
        # People mappings
        if contacts:
            all_mappings.update(self.get_people_mappings(contacts))
        
        return all_mappings
    
    def add_field_mapping(self, category: str, old_text: str, field_config: Dict[str, Any]):
        """Add a new field mapping"""
        if category not in self.field_mappings:
            self.field_mappings[category] = {}
        
        self.field_mappings[category][old_text] = field_config
        logger.info(f"Added field mapping: {category}.{old_text}")
    
    def remove_field_mapping(self, category: str, old_text: str):
        """Remove a field mapping"""
        if category in self.field_mappings and old_text in self.field_mappings[category]:
            del self.field_mappings[category][old_text]
            logger.info(f"Removed field mapping: {category}.{old_text}")
    
    def list_field_mappings(self) -> Dict[str, List[str]]:
        """List all configured field mappings by category"""
        result = {}
        for category, fields in self.field_mappings.items():
            result[category] = list(fields.keys())
        return result
    
    def validate_mappings(self, template_content: str) -> Tuple[List[str], List[str]]:
        """Validate field mappings against template content
        
        Returns:
            Tuple of (found_fields, missing_fields)
        """
        found_fields = []
        missing_fields = []
        
        for category, fields in self.field_mappings.items():
            for old_text in fields.keys():
                if old_text in template_content:
                    found_fields.append(f"{category}.{old_text}")
                else:
                    missing_fields.append(f"{category}.{old_text}")
        
        return found_fields, missing_fields
