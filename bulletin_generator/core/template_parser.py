"""
SVG Template Parser for Church Bulletin Generator
"""

import re
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from xml.etree import ElementTree as ET
from xml.dom import minidom

from ..config import get_config

logger = logging.getLogger(__name__)

class TemplateParser:
    """Parser for SVG bulletin templates"""
    
    def __init__(self, template_path: Optional[Path] = None):
        """Initialize template parser"""
        self.config = get_config()
        self.template_path = template_path or self._get_default_template_path()
        self.template_content = None
        self.placeholders = {}
        
    def _get_default_template_path(self) -> Path:
        """Get path to default template"""
        templates_dir = self.config['paths']['templates_dir']
        return templates_dir / self.config['templates']['default_template']
    
    def load_template(self, template_path: Optional[Path] = None) -> str:
        """Load SVG template content"""
        path = template_path or self.template_path
        
        try:
            with open(path, 'r', encoding=self.config['templates']['encoding']) as f:
                self.template_content = f.read()
            logger.info(f"Template loaded from {path}")
            return self.template_content
        except Exception as e:
            logger.error(f"Error loading template from {path}: {e}")
            raise
    
    def find_text_elements(self, content: Optional[str] = None) -> List[Dict[str, str]]:
        """Find all text elements in SVG that could be placeholders"""
        content = content or self.template_content
        if not content:
            raise ValueError("No template content loaded")
        
        text_elements = []
        
        # Parse XML
        try:
            root = ET.fromstring(content)
            
            # Find all tspan elements with text content
            for tspan in root.iter():
                if tspan.tag.endswith('tspan') and tspan.text:
                    text_content = tspan.text.strip()
                    if text_content:
                        text_elements.append({
                            'id': tspan.get('id', ''),
                            'text': text_content,
                            'element': tspan
                        })
            
        except ET.ParseError as e:
            logger.error(f"Error parsing SVG template: {e}")
            # Fallback to regex parsing
            text_elements = self._regex_find_text_elements(content)
        
        return text_elements
    
    def _regex_find_text_elements(self, content: str) -> List[Dict[str, str]]:
        """Fallback regex-based text element finder"""
        text_elements = []
        
        # Pattern to find tspan elements with text content
        pattern = r'<tspan[^>]*id="([^"]*)"[^>]*>([^<]+)</tspan>'
        matches = re.finditer(pattern, content)
        
        for match in matches:
            element_id, text = match.groups()
            text = text.strip()
            if text:
                text_elements.append({
                    'id': element_id,
                    'text': text,
                    'match': match
                })
        
        return text_elements
    
    def identify_placeholders(self, content: Optional[str] = None) -> Dict[str, List[str]]:
        """Identify potential placeholders based on content patterns"""
        content = content or self.template_content
        text_elements = self.find_text_elements(content)
        
        placeholders = {
            'dates': [],
            'times': [],
            'locations': [],
            'names': [],
            'other': []
        }
        
        # Patterns for different types of content
        date_patterns = [
            r'\d{1,2}-[A-Za-z]{3}',  # 10-Aug
            r'\d{1,2}\s+[A-Za-z]+',  # 10 August
            r'[A-Za-z]+,?\s+\d{1,2}',  # Sunday, 10
        ]
        
        time_patterns = [
            r'\d{1,2}\.\d{2}[ap]m',  # 7.45am, 9.00am
            r'\d{1,2}:\d{2}[ap]m',   # 7:45am, 9:00am
        ]
        
        location_patterns = [
            r'Beachmere',
            r'Caboolture',
            r'Upper Caboolture',
            r'Tongan Service'
        ]
        
        name_patterns = [
            r'Rev\.\s+\w+\s+\w+',  # Rev. Jason Grimsey
            r'[A-Z][a-z]+\s+[A-Z][a-z]+',  # First Last names
        ]
        
        for element in text_elements:
            text = element['text']
            element_id = element['id']
            
            # Check for dates
            for pattern in date_patterns:
                if re.search(pattern, text):
                    placeholders['dates'].append({
                        'id': element_id,
                        'text': text,
                        'pattern': pattern
                    })
                    break
            
            # Check for times
            for pattern in time_patterns:
                if re.search(pattern, text):
                    placeholders['times'].append({
                        'id': element_id,
                        'text': text,
                        'pattern': pattern
                    })
                    break
            
            # Check for locations
            for pattern in location_patterns:
                if re.search(pattern, text, re.IGNORECASE):
                    placeholders['locations'].append({
                        'id': element_id,
                        'text': text,
                        'pattern': pattern
                    })
                    break
            
            # Check for names
            for pattern in name_patterns:
                if re.search(pattern, text):
                    placeholders['names'].append({
                        'id': element_id,
                        'text': text,
                        'pattern': pattern
                    })
                    break
            
            # If no pattern matched, add to other
            if not any(text in cat for cat in placeholders.values()):
                placeholders['other'].append({
                    'id': element_id,
                    'text': text
                })
        
        self.placeholders = placeholders
        return placeholders
    
    def replace_content(self, replacements: Dict[str, str], 
                       content: Optional[str] = None) -> str:
        """Replace content in template with new values"""
        content = content or self.template_content
        if not content:
            raise ValueError("No template content loaded")
        
        modified_content = content
        
        # Replace based on exact text matches first
        for old_text, new_text in replacements.items():
            # Use regex to replace text within tspan elements
            pattern = f'(<tspan[^>]*>){re.escape(old_text)}(</tspan>)'
            replacement = f'\\g<1>{new_text}\\g<2>'
            modified_content = re.sub(pattern, replacement, modified_content)
        
        return modified_content
    
    def replace_by_id(self, id_replacements: Dict[str, str], 
                     content: Optional[str] = None) -> str:
        """Replace content by element ID"""
        content = content or self.template_content
        if not content:
            raise ValueError("No template content loaded")
        
        modified_content = content
        
        for element_id, new_text in id_replacements.items():
            # Pattern to find tspan with specific ID and replace its content
            pattern = f'(<tspan[^>]*id="{re.escape(element_id)}"[^>]*>)[^<]*(</tspan>)'
            replacement = f'\\g<1>{new_text}\\g<2>'
            modified_content = re.sub(pattern, replacement, modified_content)
        
        return modified_content
    
    def save_template(self, content: str, output_path: Path) -> None:
        """Save modified template to file"""
        try:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding=self.config['templates']['encoding']) as f:
                f.write(content)
            logger.info(f"Template saved to {output_path}")
        except Exception as e:
            logger.error(f"Error saving template to {output_path}: {e}")
            raise
    
    def get_template_info(self) -> Dict[str, any]:
        """Get information about the loaded template"""
        if not self.template_content:
            return {}
        
        text_elements = self.find_text_elements()
        placeholders = self.identify_placeholders()
        
        return {
            'template_path': str(self.template_path),
            'text_elements_count': len(text_elements),
            'placeholders': {
                'dates': len(placeholders['dates']),
                'times': len(placeholders['times']),
                'locations': len(placeholders['locations']),
                'names': len(placeholders['names']),
                'other': len(placeholders['other'])
            },
            'total_placeholders': sum(len(v) for v in placeholders.values())
        }
