"""
Content Generation Engine for Church Bulletin Generator
"""

import logging
from datetime import date, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from .database import BulletinDatabase
from .template_parser import TemplateParser
from .field_mapper import FieldMapper
from ..models.bulletin import BulletinData
from ..models.service import ServiceTime
from ..models.event import Event
from ..models.bible_verse import BibleVerse
from ..models.contact import Contact
from ..config import get_config
from ..utils.field_config_manager import FieldConfigManager

logger = logging.getLogger(__name__)

class ContentGenerator:
    """Generates bulletin content from database and templates with enhanced field mapping"""

    def __init__(self, database: BulletinDatabase, field_config_path: Optional[Path] = None):
        """Initialize content generator"""
        self.db = database
        self.config = get_config()

        # Initialize field mapping system
        self.field_config_manager = FieldConfigManager(field_config_path)
        self.field_mapper = FieldMapper()
        self.template_parser = TemplateParser(field_mapper=self.field_mapper)
        
    def generate_bulletin_data(self, bulletin_date: date) -> BulletinData:
        """Generate complete bulletin data for a specific date"""
        logger.info(f"Generating bulletin data for {bulletin_date}")
        
        # Get or create bulletin metadata
        metadata = self.db.get_bulletin_metadata(bulletin_date)
        if not metadata:
            # Create default metadata
            self.db.add_bulletin_metadata(
                date=bulletin_date,
                title=f"Weekly Bulletin - {bulletin_date.strftime('%d %B %Y')}"
            )
            metadata = self.db.get_bulletin_metadata(bulletin_date)
        
        # Get service times
        service_times = self.db.get_service_times(bulletin_date)
        if not service_times:
            # Create default service times if none exist
            service_times = self._create_default_service_times(bulletin_date)
        
        # Get events
        events = self.db.get_events(bulletin_date)
        
        # Get Bible verse
        bible_verse = self.db.get_bible_verse(bulletin_date)
        
        # Get contacts
        minister = self.db.get_contact_by_role('Minister')
        office_contact = self.db.get_contact_by_role('Administration')
        
        # Create bulletin data object
        bulletin_data = BulletinData(
            date=bulletin_date,
            title=metadata.get('title'),
            edition_number=metadata.get('edition_number'),
            theme=metadata.get('theme'),
            service_times=service_times,
            bible_verse=bible_verse,
            events=events,
            minister=minister,
            office_contact=office_contact,
            special_notes=metadata.get('special_notes'),
            template_version=metadata.get('template_version', 'standard')
        )
        
        return bulletin_data
    
    def _create_default_service_times(self, bulletin_date: date) -> List[ServiceTime]:
        """Create default service times for a date"""
        default_services = []
        service_locations = self.config['services']['locations']
        
        for location, time in service_locations.items():
            service_id = self.db.add_service_time(
                date=bulletin_date,
                location=location,
                time=time,
                preacher="Rev. Jason Grimsey"  # Default preacher
            )
            
            service = ServiceTime(
                id=service_id,
                date=bulletin_date,
                location=location,
                time=time,
                preacher="Rev. Jason Grimsey"
            )
            default_services.append(service)
        
        return default_services
    
    def generate_bulletin(self, bulletin_data: BulletinData, 
                         output_format: str = 'pdf',
                         output_path: Optional[Path] = None) -> Path:
        """Generate final bulletin file"""
        logger.info(f"Generating bulletin in {output_format} format")
        
        # Load template
        self.template_parser.load_template()
        
        # Replace content in template using new field mapping system
        modified_content = self._apply_template_replacements(bulletin_data)
        
        # Determine output path
        if not output_path:
            output_dir = self.config['paths']['output_dir']
            filename = f"bulletin_{bulletin_data.date.strftime('%Y%m%d')}.{output_format}"
            output_path = output_dir / filename
        
        # Save based on format
        if output_format == 'svg':
            self.template_parser.save_template(modified_content, output_path)
        elif output_format == 'pdf':
            output_path = self._convert_to_pdf(modified_content, output_path)
        elif output_format == 'png':
            output_path = self._convert_to_png(modified_content, output_path)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
        
        logger.info(f"Bulletin generated: {output_path}")
        return output_path
    
    def _apply_template_replacements(self, bulletin_data: BulletinData) -> str:
        """Apply variable replacements to template using the new field mapping system"""
        # Load current template content
        content = self.template_parser.template_content

        # Prepare contacts dictionary
        contacts = {
            'minister': bulletin_data.minister,
            'office': bulletin_data.office_contact
        }

        # Get all field mappings using the new system
        all_mappings = self.field_mapper.get_all_mappings(
            bulletin_date=bulletin_data.date,
            service_times=bulletin_data.service_times,
            contacts=contacts
        )

        # Apply replacements using the template parser
        modified_content = self.template_parser.replace_content(all_mappings, content)

        return modified_content

    def analyze_template_fields(self) -> Dict[str, Any]:
        """Analyze template to detect all replaceable fields and compare with configuration"""
        # Load template if not already loaded
        if not self.template_parser.template_content:
            self.template_parser.load_template()

        # Detect all fields in template
        detected_fields = self.template_parser.detect_all_fields()

        # Compare with configured mappings
        comparison = self.template_parser.compare_with_field_mappings(self.field_mapper)

        # Get suggestions for unmapped fields
        suggestions = self.template_parser.suggest_field_mappings()

        return {
            'detected_fields': detected_fields,
            'field_comparison': comparison,
            'suggestions': suggestions,
            'template_path': str(self.template_parser.template_path)
        }

    def validate_field_mappings(self) -> Dict[str, Any]:
        """Validate current field mappings against the template"""
        # Load template if not already loaded
        if not self.template_parser.template_content:
            self.template_parser.load_template()

        # Validate configuration
        config_valid, config_errors = self.field_config_manager.validate_config()

        # Validate mappings against template
        found_fields, missing_fields = self.field_mapper.validate_mappings(
            self.template_parser.template_content
        )

        return {
            'config_valid': config_valid,
            'config_errors': config_errors,
            'found_fields': found_fields,
            'missing_fields': missing_fields,
            'total_configured': len(found_fields) + len(missing_fields),
            'coverage_percentage': len(found_fields) / (len(found_fields) + len(missing_fields)) * 100 if (found_fields or missing_fields) else 0
        }

    def update_field_mappings_from_template(self) -> Dict[str, Any]:
        """Auto-update field mappings based on template analysis"""
        analysis = self.analyze_template_fields()
        suggestions = analysis['suggestions']

        added_count = 0
        for category, fields in suggestions.items():
            for field_text, field_config in fields.items():
                self.field_mapper.add_field_mapping(category, field_text, field_config)
                added_count += 1

        return {
            'added_mappings': added_count,
            'suggestions_applied': suggestions,
            'analysis': analysis
        }
    
    def _convert_to_pdf(self, svg_content: str, output_path: Path) -> Path:
        """Convert SVG content to PDF"""
        try:
            import cairosvg
            
            # Ensure output path has .pdf extension
            if output_path.suffix != '.pdf':
                output_path = output_path.with_suffix('.pdf')
            
            # Convert SVG to PDF
            cairosvg.svg2pdf(
                bytestring=svg_content.encode('utf-8'),
                write_to=str(output_path),
                dpi=self.config['pdf']['dpi']
            )
            
            return output_path
            
        except ImportError:
            logger.error("cairosvg not installed. Cannot convert to PDF.")
            raise
        except Exception as e:
            logger.error(f"Error converting to PDF: {e}")
            raise
    
    def _convert_to_png(self, svg_content: str, output_path: Path) -> Path:
        """Convert SVG content to PNG"""
        try:
            import cairosvg
            
            # Ensure output path has .png extension
            if output_path.suffix != '.png':
                output_path = output_path.with_suffix('.png')
            
            # Convert SVG to PNG
            cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                write_to=str(output_path),
                dpi=self.config['pdf']['dpi']
            )
            
            return output_path
            
        except ImportError:
            logger.error("cairosvg not installed. Cannot convert to PNG.")
            raise
        except Exception as e:
            logger.error(f"Error converting to PNG: {e}")
            raise
    
    def preview_bulletin_data(self, bulletin_date: date) -> Dict[str, Any]:
        """Generate preview data for bulletin without creating files"""
        bulletin_data = self.generate_bulletin_data(bulletin_date)
        
        return {
            'date': bulletin_data.date.strftime('%A, %d %B %Y'),
            'title': bulletin_data.title,
            'service_times': [
                {
                    'location': service.location,
                    'time': service.time,
                    'preacher': service.preacher
                }
                for service in bulletin_data.service_times
            ],
            'events_count': len(bulletin_data.events),
            'events': [
                {
                    'title': event.title,
                    'description': event.get_formatted_description()
                }
                for event in bulletin_data.events[:5]  # Show first 5 events
            ],
            'bible_verse': bulletin_data.bible_verse.get_formatted_verse() if bulletin_data.bible_verse else None,
            'minister': bulletin_data.minister.name if bulletin_data.minister else None
        }
