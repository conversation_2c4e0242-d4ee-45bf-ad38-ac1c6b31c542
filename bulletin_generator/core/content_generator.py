"""
Content Generation Engine for Church Bulletin Generator
"""

import logging
from datetime import date, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

from .database import BulletinDatabase
from .template_parser import TemplateParser
from ..models.bulletin import BulletinData
from ..models.service import ServiceTime
from ..models.event import Event
from ..models.bible_verse import Bible<PERSON>erse
from ..models.contact import Contact
from ..config import get_config

logger = logging.getLogger(__name__)

class ContentGenerator:
    """Generates bulletin content from database and templates"""
    
    def __init__(self, database: BulletinDatabase):
        """Initialize content generator"""
        self.db = database
        self.config = get_config()
        self.template_parser = TemplateParser()
        
    def generate_bulletin_data(self, bulletin_date: date) -> BulletinData:
        """Generate complete bulletin data for a specific date"""
        logger.info(f"Generating bulletin data for {bulletin_date}")
        
        # Get or create bulletin metadata
        metadata = self.db.get_bulletin_metadata(bulletin_date)
        if not metadata:
            # Create default metadata
            self.db.add_bulletin_metadata(
                date=bulletin_date,
                title=f"Weekly Bulletin - {bulletin_date.strftime('%d %B %Y')}"
            )
            metadata = self.db.get_bulletin_metadata(bulletin_date)
        
        # Get service times
        service_times = self.db.get_service_times(bulletin_date)
        if not service_times:
            # Create default service times if none exist
            service_times = self._create_default_service_times(bulletin_date)
        
        # Get events
        events = self.db.get_events(bulletin_date)
        
        # Get Bible verse
        bible_verse = self.db.get_bible_verse(bulletin_date)
        
        # Get contacts
        minister = self.db.get_contact_by_role('Minister')
        office_contact = self.db.get_contact_by_role('Administration')
        
        # Create bulletin data object
        bulletin_data = BulletinData(
            date=bulletin_date,
            title=metadata.get('title'),
            edition_number=metadata.get('edition_number'),
            theme=metadata.get('theme'),
            service_times=service_times,
            bible_verse=bible_verse,
            events=events,
            minister=minister,
            office_contact=office_contact,
            special_notes=metadata.get('special_notes'),
            template_version=metadata.get('template_version', 'standard')
        )
        
        return bulletin_data
    
    def _create_default_service_times(self, bulletin_date: date) -> List[ServiceTime]:
        """Create default service times for a date"""
        default_services = []
        service_locations = self.config['services']['locations']
        
        for location, time in service_locations.items():
            service_id = self.db.add_service_time(
                date=bulletin_date,
                location=location,
                time=time,
                preacher="Rev. Jason Grimsey"  # Default preacher
            )
            
            service = ServiceTime(
                id=service_id,
                date=bulletin_date,
                location=location,
                time=time,
                preacher="Rev. Jason Grimsey"
            )
            default_services.append(service)
        
        return default_services
    
    def generate_bulletin(self, bulletin_data: BulletinData, 
                         output_format: str = 'pdf',
                         output_path: Optional[Path] = None) -> Path:
        """Generate final bulletin file"""
        logger.info(f"Generating bulletin in {output_format} format")
        
        # Load template
        self.template_parser.load_template()
        
        # Get template variables
        template_vars = bulletin_data.get_template_variables()
        
        # Replace content in template
        modified_content = self._apply_template_replacements(template_vars)
        
        # Determine output path
        if not output_path:
            output_dir = self.config['paths']['output_dir']
            filename = f"bulletin_{bulletin_data.date.strftime('%Y%m%d')}.{output_format}"
            output_path = output_dir / filename
        
        # Save based on format
        if output_format == 'svg':
            self.template_parser.save_template(modified_content, output_path)
        elif output_format == 'pdf':
            output_path = self._convert_to_pdf(modified_content, output_path)
        elif output_format == 'png':
            output_path = self._convert_to_png(modified_content, output_path)
        else:
            raise ValueError(f"Unsupported output format: {output_format}")
        
        logger.info(f"Bulletin generated: {output_path}")
        return output_path
    
    def _apply_template_replacements(self, template_vars: Dict[str, str]) -> str:
        """Apply variable replacements to template"""
        # Load current template content
        content = self.template_parser.template_content
        
        # Create mapping of old values to new values based on analysis
        replacements = {}
        
        # Map dates
        if 'bulletin_date' in template_vars:
            # Replace common date patterns found in template
            replacements['10-Aug'] = template_vars['bulletin_date']
        
        # Map service times
        service_locations = ['beachmere', 'caboolture', 'upper_caboolture', 'tongan_service']
        for location in service_locations:
            time_key = f'{location}_time'
            if time_key in template_vars:
                # Map to known time patterns in template
                if location == 'beachmere' and '7.45am' in content:
                    replacements['7.45am'] = template_vars[time_key]
                elif location == 'caboolture' and '9.00am' in content:
                    replacements['9.00am'] = template_vars[time_key]
                elif location == 'upper_caboolture' and '10.45am' in content:
                    replacements['10.45am'] = template_vars[time_key]
                elif location == 'tongan_service' and '1.00pm' in content:
                    replacements['1.00pm'] = template_vars[time_key]
        
        # Map preacher names
        if 'minister_name' in template_vars:
            replacements['Rev. Jason Grimsey'] = template_vars['minister_name']
        
        # Apply replacements
        modified_content = self.template_parser.replace_content(replacements, content)
        
        return modified_content
    
    def _convert_to_pdf(self, svg_content: str, output_path: Path) -> Path:
        """Convert SVG content to PDF"""
        try:
            import cairosvg
            
            # Ensure output path has .pdf extension
            if output_path.suffix != '.pdf':
                output_path = output_path.with_suffix('.pdf')
            
            # Convert SVG to PDF
            cairosvg.svg2pdf(
                bytestring=svg_content.encode('utf-8'),
                write_to=str(output_path),
                dpi=self.config['pdf']['dpi']
            )
            
            return output_path
            
        except ImportError:
            logger.error("cairosvg not installed. Cannot convert to PDF.")
            raise
        except Exception as e:
            logger.error(f"Error converting to PDF: {e}")
            raise
    
    def _convert_to_png(self, svg_content: str, output_path: Path) -> Path:
        """Convert SVG content to PNG"""
        try:
            import cairosvg
            
            # Ensure output path has .png extension
            if output_path.suffix != '.png':
                output_path = output_path.with_suffix('.png')
            
            # Convert SVG to PNG
            cairosvg.svg2png(
                bytestring=svg_content.encode('utf-8'),
                write_to=str(output_path),
                dpi=self.config['pdf']['dpi']
            )
            
            return output_path
            
        except ImportError:
            logger.error("cairosvg not installed. Cannot convert to PNG.")
            raise
        except Exception as e:
            logger.error(f"Error converting to PNG: {e}")
            raise
    
    def preview_bulletin_data(self, bulletin_date: date) -> Dict[str, Any]:
        """Generate preview data for bulletin without creating files"""
        bulletin_data = self.generate_bulletin_data(bulletin_date)
        
        return {
            'date': bulletin_data.date.strftime('%A, %d %B %Y'),
            'title': bulletin_data.title,
            'service_times': [
                {
                    'location': service.location,
                    'time': service.time,
                    'preacher': service.preacher
                }
                for service in bulletin_data.service_times
            ],
            'events_count': len(bulletin_data.events),
            'events': [
                {
                    'title': event.title,
                    'description': event.get_formatted_description()
                }
                for event in bulletin_data.events[:5]  # Show first 5 events
            ],
            'bible_verse': bulletin_data.bible_verse.get_formatted_verse() if bulletin_data.bible_verse else None,
            'minister': bulletin_data.minister.name if bulletin_data.minister else None
        }
