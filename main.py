#!/usr/bin/env python3
"""
Church Bulletin Generator - Main Entry Point

This script provides a command-line interface for generating church bulletins
from templates and database content.

Usage:
    python main.py generate --date 2025-08-10
    python main.py init-db
    python main.py add-event --date 2025-08-10 --title "Special Service"
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, date
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bulletin_generator.config import get_config, ensure_directories
from bulletin_generator.core.database import BulletinDatabase
from bulletin_generator.core.template_parser import TemplateParser
from bulletin_generator.core.content_generator import ContentGenerator
from bulletin_generator.core.field_mapper import FieldMapper
from bulletin_generator.utils.field_config_manager import FieldConfigManager

# Initialize rich console for beautiful output
console = Console()

def setup_logging():
    """Setup logging configuration"""
    config = get_config()
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bulletin_generator.log'),
            logging.StreamHandler()
        ]
    )

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Church Bulletin Generator - Automated bulletin creation system"""
    setup_logging()
    ensure_directories()

@cli.command()
def init_db():
    """Initialize the database with schema and sample data"""
    try:
        console.print("[bold blue]Initializing database...[/bold blue]")
        db = BulletinDatabase()
        db.initialize_database()
        console.print("[bold green]✓ Database initialized successfully![/bold green]")
    except Exception as e:
        console.print(f"[bold red]✗ Error initializing database: {e}[/bold red]")
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              default=str(date.today()), help='Bulletin date (YYYY-MM-DD)')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--format', '-f', type=click.Choice(['pdf', 'svg', 'png']), 
              default='pdf', help='Output format')
def generate(date, output, format):
    """Generate a bulletin for the specified date"""
    try:
        console.print(f"[bold blue]Generating bulletin for {date.strftime('%A, %B %d, %Y')}...[/bold blue]")
        
        # Initialize components
        db = BulletinDatabase()
        parser = TemplateParser()
        generator = ContentGenerator(db)
        
        # Generate bulletin
        bulletin_data = generator.generate_bulletin_data(date.date())
        output_path = generator.generate_bulletin(bulletin_data, output_format=format, output_path=output)
        
        console.print(f"[bold green]✓ Bulletin generated successfully![/bold green]")
        console.print(f"[blue]Output saved to: {output_path}[/blue]")
        
    except Exception as e:
        console.print(f"[bold red]✗ Error generating bulletin: {e}[/bold red]")
        logging.error(f"Error generating bulletin: {e}", exc_info=True)
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              required=True, help='Event date (YYYY-MM-DD)')
@click.option('--title', '-t', required=True, help='Event title')
@click.option('--description', help='Event description')
@click.option('--time', help='Event time')
@click.option('--location', help='Event location')
@click.option('--contact', help='Contact person')
def add_event(date, title, description, time, location, contact):
    """Add a new event to the database"""
    try:
        db = BulletinDatabase()
        event_id = db.add_event(
            date=date.date(),
            title=title,
            description=description,
            time=time,
            location=location,
            contact_person=contact
        )
        console.print(f"[bold green]✓ Event added successfully! (ID: {event_id})[/bold green]")
    except Exception as e:
        console.print(f"[bold red]✗ Error adding event: {e}[/bold red]")
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              help='Show events for specific date')
def list_events(date):
    """List events in the database"""
    try:
        db = BulletinDatabase()
        events = db.get_events(date.date() if date else None)
        
        if not events:
            console.print("[yellow]No events found.[/yellow]")
            return
            
        table = Table(title="Church Events")
        table.add_column("Date", style="cyan")
        table.add_column("Title", style="magenta")
        table.add_column("Time", style="green")
        table.add_column("Location", style="blue")
        
        for event in events:
            table.add_row(
                str(event.date),
                event.title,
                event.time or "",
                event.location or ""
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[bold red]✗ Error listing events: {e}[/bold red]")
        sys.exit(1)

@cli.command()
def status():
    """Show system status and configuration"""
    try:
        config = get_config()
        
        # Create status panel
        status_text = f"""
[bold]Database:[/bold] {config['paths']['database_path']}
[bold]Templates:[/bold] {config['paths']['templates_dir']}
[bold]Output:[/bold] {config['paths']['output_dir']}
[bold]Default Format:[/bold] {config['output']['default_format']}
        """
        
        console.print(Panel(status_text, title="System Status", border_style="blue"))
        
        # Check database
        db = BulletinDatabase()
        if db.check_connection():
            console.print("[bold green]✓ Database connection OK[/bold green]")
        else:
            console.print("[bold red]✗ Database connection failed[/bold red]")
            
    except Exception as e:
        console.print(f"[bold red]✗ Error checking status: {e}[/bold red]")
        sys.exit(1)

@cli.group()
def fields():
    """Manage template field mappings"""
    pass

@fields.command()
@click.option('--config', '-c', type=click.Path(exists=True), help='Custom field mapping config file')
def analyze():
    """Analyze template to detect all replaceable fields"""
    try:
        console.print("[bold blue]Analyzing template fields...[/bold blue]")

        db = BulletinDatabase()
        config_path = Path(click.get_current_context().params.get('config', 'field_mappings.json'))
        generator = ContentGenerator(db, config_path)

        analysis = generator.analyze_template_fields()

        # Display detected fields
        console.print("\n[bold green]Detected Fields:[/bold green]")
        for category, fields in analysis['detected_fields'].items():
            if fields:
                console.print(f"[cyan]{category.title()}:[/cyan] {', '.join(fields)}")

        # Display field comparison
        comparison = analysis['field_comparison']
        console.print(f"\n[bold green]Field Mapping Status:[/bold green]")
        console.print(f"[green]✓ Mapped fields:[/green] {len(comparison['mapped'])}")
        console.print(f"[yellow]⚠ Unmapped fields:[/yellow] {len(comparison['unmapped'])}")
        console.print(f"[red]✗ Missing fields:[/red] {len(comparison['missing'])}")

        if comparison['unmapped']:
            console.print(f"\n[yellow]Unmapped fields found:[/yellow] {', '.join(comparison['unmapped'])}")

        if comparison['missing']:
            console.print(f"\n[red]Missing fields (configured but not found in template):[/red] {', '.join(comparison['missing'])}")

    except Exception as e:
        console.print(f"[bold red]✗ Error analyzing fields: {e}[/bold red]")
        sys.exit(1)

@fields.command()
@click.option('--config', '-c', type=click.Path(exists=True), help='Custom field mapping config file')
def validate():
    """Validate current field mappings"""
    try:
        console.print("[bold blue]Validating field mappings...[/bold blue]")

        db = BulletinDatabase()
        config_path = Path(click.get_current_context().params.get('config', 'field_mappings.json'))
        generator = ContentGenerator(db, config_path)

        validation = generator.validate_field_mappings()

        if validation['config_valid']:
            console.print("[bold green]✓ Configuration is valid[/bold green]")
        else:
            console.print("[bold red]✗ Configuration has errors:[/bold red]")
            for error in validation['config_errors']:
                console.print(f"  [red]• {error}[/red]")

        console.print(f"\n[bold]Field Coverage:[/bold] {validation['coverage_percentage']:.1f}%")
        console.print(f"[green]Found in template:[/green] {len(validation['found_fields'])}")
        console.print(f"[red]Missing from template:[/red] {len(validation['missing_fields'])}")

        if validation['missing_fields']:
            console.print(f"\n[red]Missing fields:[/red]")
            for field in validation['missing_fields']:
                console.print(f"  [red]• {field}[/red]")

    except Exception as e:
        console.print(f"[bold red]✗ Error validating fields: {e}[/bold red]")
        sys.exit(1)

@fields.command()
@click.option('--config', '-c', type=click.Path(), help='Custom field mapping config file')
@click.option('--backup/--no-backup', default=True, help='Create backup before updating')
def auto_update():
    """Auto-update field mappings based on template analysis"""
    try:
        console.print("[bold blue]Auto-updating field mappings...[/bold blue]")

        db = BulletinDatabase()
        config_path = Path(click.get_current_context().params.get('config', 'field_mappings.json'))
        generator = ContentGenerator(db, config_path)

        # Create backup if requested
        backup = click.get_current_context().params.get('backup', True)
        if backup and config_path.exists():
            backup_path = config_path.with_suffix(f'.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
            import shutil
            shutil.copy2(config_path, backup_path)
            console.print(f"[blue]Backup created: {backup_path}[/blue]")

        result = generator.update_field_mappings_from_template()

        console.print(f"[bold green]✓ Added {result['added_mappings']} new field mappings[/bold green]")

        if result['added_mappings'] > 0:
            console.print("\n[bold]New mappings added:[/bold]")
            for category, fields in result['suggestions_applied'].items():
                console.print(f"[cyan]{category}:[/cyan]")
                for field_text in fields.keys():
                    console.print(f"  [green]• {field_text}[/green]")

    except Exception as e:
        console.print(f"[bold red]✗ Error auto-updating fields: {e}[/bold red]")
        sys.exit(1)

@fields.command()
@click.option('--config', '-c', type=click.Path(), help='Custom field mapping config file')
def list_mappings():
    """List all configured field mappings"""
    try:
        config_path = Path(click.get_current_context().params.get('config', 'field_mappings.json'))
        config_manager = FieldConfigManager(config_path)

        mappings = config_manager.get_all_field_mappings()

        console.print("[bold blue]Configured Field Mappings:[/bold blue]")

        for category, fields in mappings.items():
            if fields:
                console.print(f"\n[bold cyan]{category.replace('_', ' ').title()}:[/bold cyan]")
                for field_text, field_config in fields.items():
                    description = field_config.get('description', 'No description')
                    console.print(f"  [green]• {field_text}[/green] - {description}")

    except Exception as e:
        console.print(f"[bold red]✗ Error listing mappings: {e}[/bold red]")
        sys.exit(1)

@fields.command()
@click.option('--output', '-o', type=click.Path(), default='field_mappings_template.json',
              help='Output path for template file')
def export_template():
    """Export a template configuration file for easy editing"""
    try:
        output_path = Path(click.get_current_context().params['output'])
        config_manager = FieldConfigManager()
        config_manager.export_template(output_path)

        console.print(f"[bold green]✓ Template exported to {output_path}[/bold green]")
        console.print("[blue]Edit this file to customize your field mappings, then use it with --config option[/blue]")

    except Exception as e:
        console.print(f"[bold red]✗ Error exporting template: {e}[/bold red]")
        sys.exit(1)

if __name__ == '__main__':
    cli()
