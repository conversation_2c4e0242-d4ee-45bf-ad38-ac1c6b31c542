#!/usr/bin/env python3
"""
Church Bulletin Generator - Main Entry Point

This script provides a command-line interface for generating church bulletins
from templates and database content.

Usage:
    python main.py generate --date 2025-08-10
    python main.py init-db
    python main.py add-event --date 2025-08-10 --title "Special Service"
"""

import sys
import logging
from pathlib import Path
from datetime import datetime, date
import click
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bulletin_generator.config import get_config, ensure_directories
from bulletin_generator.core.database import BulletinDatabase
from bulletin_generator.core.template_parser import TemplateParser
from bulletin_generator.core.content_generator import ContentGenerator

# Initialize rich console for beautiful output
console = Console()

def setup_logging():
    """Setup logging configuration"""
    config = get_config()
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('bulletin_generator.log'),
            logging.StreamHandler()
        ]
    )

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Church Bulletin Generator - Automated bulletin creation system"""
    setup_logging()
    ensure_directories()

@cli.command()
def init_db():
    """Initialize the database with schema and sample data"""
    try:
        console.print("[bold blue]Initializing database...[/bold blue]")
        db = BulletinDatabase()
        db.initialize_database()
        console.print("[bold green]✓ Database initialized successfully![/bold green]")
    except Exception as e:
        console.print(f"[bold red]✗ Error initializing database: {e}[/bold red]")
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              default=str(date.today()), help='Bulletin date (YYYY-MM-DD)')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--format', '-f', type=click.Choice(['pdf', 'svg', 'png']), 
              default='pdf', help='Output format')
def generate(date, output, format):
    """Generate a bulletin for the specified date"""
    try:
        console.print(f"[bold blue]Generating bulletin for {date.strftime('%A, %B %d, %Y')}...[/bold blue]")
        
        # Initialize components
        db = BulletinDatabase()
        parser = TemplateParser()
        generator = ContentGenerator(db)
        
        # Generate bulletin
        bulletin_data = generator.generate_bulletin_data(date.date())
        output_path = generator.generate_bulletin(bulletin_data, output_format=format, output_path=output)
        
        console.print(f"[bold green]✓ Bulletin generated successfully![/bold green]")
        console.print(f"[blue]Output saved to: {output_path}[/blue]")
        
    except Exception as e:
        console.print(f"[bold red]✗ Error generating bulletin: {e}[/bold red]")
        logging.error(f"Error generating bulletin: {e}", exc_info=True)
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              required=True, help='Event date (YYYY-MM-DD)')
@click.option('--title', '-t', required=True, help='Event title')
@click.option('--description', help='Event description')
@click.option('--time', help='Event time')
@click.option('--location', help='Event location')
@click.option('--contact', help='Contact person')
def add_event(date, title, description, time, location, contact):
    """Add a new event to the database"""
    try:
        db = BulletinDatabase()
        event_id = db.add_event(
            date=date.date(),
            title=title,
            description=description,
            time=time,
            location=location,
            contact_person=contact
        )
        console.print(f"[bold green]✓ Event added successfully! (ID: {event_id})[/bold green]")
    except Exception as e:
        console.print(f"[bold red]✗ Error adding event: {e}[/bold red]")
        sys.exit(1)

@cli.command()
@click.option('--date', '-d', type=click.DateTime(formats=['%Y-%m-%d']), 
              help='Show events for specific date')
def list_events(date):
    """List events in the database"""
    try:
        db = BulletinDatabase()
        events = db.get_events(date.date() if date else None)
        
        if not events:
            console.print("[yellow]No events found.[/yellow]")
            return
            
        table = Table(title="Church Events")
        table.add_column("Date", style="cyan")
        table.add_column("Title", style="magenta")
        table.add_column("Time", style="green")
        table.add_column("Location", style="blue")
        
        for event in events:
            table.add_row(
                str(event.date),
                event.title,
                event.time or "",
                event.location or ""
            )
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[bold red]✗ Error listing events: {e}[/bold red]")
        sys.exit(1)

@cli.command()
def status():
    """Show system status and configuration"""
    try:
        config = get_config()
        
        # Create status panel
        status_text = f"""
[bold]Database:[/bold] {config['paths']['database_path']}
[bold]Templates:[/bold] {config['paths']['templates_dir']}
[bold]Output:[/bold] {config['paths']['output_dir']}
[bold]Default Format:[/bold] {config['output']['default_format']}
        """
        
        console.print(Panel(status_text, title="System Status", border_style="blue"))
        
        # Check database
        db = BulletinDatabase()
        if db.check_connection():
            console.print("[bold green]✓ Database connection OK[/bold green]")
        else:
            console.print("[bold red]✗ Database connection failed[/bold red]")
            
    except Exception as e:
        console.print(f"[bold red]✗ Error checking status: {e}[/bold red]")
        sys.exit(1)

if __name__ == '__main__':
    cli()
