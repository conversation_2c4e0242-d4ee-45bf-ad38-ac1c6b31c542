"""pytest-cov: avoid already-imported warning: PYTEST_DONT_REWRITE."""

__version__ = '6.2.1'

import pytest


class CoverageError(Exception):
    """Indicates that our coverage is too low"""


class PytestCovWarning(pytest.PytestWarning):
    """
    The base for all pytest-cov warnings, never raised directly.
    """


class CovDisabledWarning(PytestCovWarning):
    """
    Indicates that Coverage was manually disabled.
    """


class CovReportWarning(PytestCovWarning):
    """
    Indicates that we failed to generate a report.
    """


class CentralCovContextWarning(PytestCovWarning):
    """
    Indicates that dynamic_context was set to test_function instead of using the builtin --cov-context.
    """


class DistCovError(Exception):
    """
    Raised when dynamic_context is set to test_function and xdist is also used.

    See: https://github.com/pytest-dev/pytest-cov/issues/604
    """
