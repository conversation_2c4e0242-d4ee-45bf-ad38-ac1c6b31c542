# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <AUTHOR> <EMAIL>, 2019
# <PERSON> <<EMAIL>>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-02-06 15:36+0100\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2021\n"
"Language-Team: Russian (https://www.transifex.com/readthedocs/teams/101354/ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || (n%100>=11 && n%100<=14)? 2 : 3);\n"

#. This is an ARIA section label for page links, including previous/next page
#. link and links to GitHub/GitLab/etc.
#: sphinx_rtd_theme/breadcrumbs.html:22
msgid "Page navigation"
msgstr "Навигация по страницам"

#: sphinx_rtd_theme/breadcrumbs.html:37 sphinx_rtd_theme/breadcrumbs.html:39
msgid "Edit on GitHub"
msgstr "Редактировать на GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:44 sphinx_rtd_theme/breadcrumbs.html:46
msgid "Edit on Bitbucket"
msgstr "Редактировать на BitBucket"

#: sphinx_rtd_theme/breadcrumbs.html:51 sphinx_rtd_theme/breadcrumbs.html:53
msgid "Edit on GitLab"
msgstr "Редактировать на GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:56 sphinx_rtd_theme/breadcrumbs.html:58
msgid "View page source"
msgstr "Просмотреть исходный код страницы"

#. This is an ARIA section label for sequential page links, such as previous
#. and next page links.
#: sphinx_rtd_theme/breadcrumbs.html:67
msgid "Sequential page navigation"
msgstr "Навигация по соседним страницам"

#: sphinx_rtd_theme/breadcrumbs.html:69 sphinx_rtd_theme/footer.html:6
msgid "Previous"
msgstr "Предыдущая"

#: sphinx_rtd_theme/breadcrumbs.html:72 sphinx_rtd_theme/footer.html:9
msgid "Next"
msgstr "Следующая"

#. This is an ARIA section label for the footer section of the page.
#: sphinx_rtd_theme/footer.html:4
msgid "Footer"
msgstr "Нижняя область"

#: sphinx_rtd_theme/footer.html:21
#, python-format
msgid "&#169; <a href=\"%(path)s\">Copyright</a> %(copyright)s."
msgstr "&#169; <a href=\"%(path)s\">Авторские права</a> %(copyright)s. "

#: sphinx_rtd_theme/footer.html:23
#, python-format
msgid "&#169; Copyright %(copyright)s."
msgstr "&#169; Авторские права %(copyright)s. "

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:30
msgid "Build"
msgstr "Сборка"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:36
msgid "Revision"
msgstr "Ревизия"

#: sphinx_rtd_theme/footer.html:41
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Последний раз обновлено %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:53
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Собрано при помощи %(sphinx_web)s с использованием"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documentation
#: sphinx_rtd_theme/footer.html:55
msgid "theme"
msgstr "темы,"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:57
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "предоставленной %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:97
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Поиск в %(docstitle)s"

#: sphinx_rtd_theme/layout.html:105
msgid "About these documents"
msgstr "Об этих документах"

#: sphinx_rtd_theme/layout.html:108
msgid "Index"
msgstr "Алфавитный указатель"

#: sphinx_rtd_theme/layout.html:111 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Поиск"

#: sphinx_rtd_theme/layout.html:114
msgid "Copyright"
msgstr "Авторские права"

#: sphinx_rtd_theme/layout.html:143
msgid "Logo"
msgstr "Логотип"

#. This is an ARIA section label for the main navigation menu
#: sphinx_rtd_theme/layout.html:166
msgid "Navigation menu"
msgstr "Меню навигации"

#. This is an ARIA section label for the navigation menu that is visible when
#. viewing the page on mobile devices
#: sphinx_rtd_theme/layout.html:188
msgid "Mobile navigation menu"
msgstr "Меню навигации для мобильных устройств"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "Активируйте JavaScript, чтобы использовать функционал поиска."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Результаты поиска"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"По Вашему запросу не найдено результатов. Пожалуйста, проверьте, что все "
"слова написаны правильно, и Вы выбрали нужные категории."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Поиск в документации"

#: sphinx_rtd_theme/versions.html:3 sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Версии"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Загрузки"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "На Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Домашняя страница проекта"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Сборки"
