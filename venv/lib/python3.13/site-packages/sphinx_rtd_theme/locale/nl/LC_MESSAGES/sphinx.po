# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON>, 2021
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-02-06 15:36+0100\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON>, 2021\n"
"Language-Team: Dutch (https://www.transifex.com/readthedocs/teams/101354/nl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"
"Language: nl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. This is an ARIA section label for page links, including previous/next page
#. link and links to GitHub/GitLab/etc.
#: sphinx_rtd_theme/breadcrumbs.html:22
msgid "Page navigation"
msgstr "Paginanavigatie"

#: sphinx_rtd_theme/breadcrumbs.html:37 sphinx_rtd_theme/breadcrumbs.html:39
msgid "Edit on GitHub"
msgstr "Bewerk op GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:44 sphinx_rtd_theme/breadcrumbs.html:46
msgid "Edit on Bitbucket"
msgstr "Bewerk op BitBucket"

#: sphinx_rtd_theme/breadcrumbs.html:51 sphinx_rtd_theme/breadcrumbs.html:53
msgid "Edit on GitLab"
msgstr "Bewerk op GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:56 sphinx_rtd_theme/breadcrumbs.html:58
msgid "View page source"
msgstr "Bekijk paginabron"

#. This is an ARIA section label for sequential page links, such as previous
#. and next page links.
#: sphinx_rtd_theme/breadcrumbs.html:67
msgid "Sequential page navigation"
msgstr "Navigatie voor gerelateerde pagina's"

#: sphinx_rtd_theme/breadcrumbs.html:69 sphinx_rtd_theme/footer.html:6
msgid "Previous"
msgstr "Vorige"

#: sphinx_rtd_theme/breadcrumbs.html:72 sphinx_rtd_theme/footer.html:9
msgid "Next"
msgstr "Volgende"

#. This is an ARIA section label for the footer section of the page.
#: sphinx_rtd_theme/footer.html:4
msgid "Footer"
msgstr "Voettekst"

#: sphinx_rtd_theme/footer.html:21
#, python-format
msgid "&#169; <a href=\"%(path)s\">Copyright</a> %(copyright)s."
msgstr "&#169; <a href=\"%(path)s\">Copyright</a> %(copyright)s."

#: sphinx_rtd_theme/footer.html:23
#, python-format
msgid "&#169; Copyright %(copyright)s."
msgstr "&#169; Copyright %(copyright)s."

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:30
msgid "Build"
msgstr "Bouwresultaat"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:36
msgid "Revision"
msgstr "Revisie"

#: sphinx_rtd_theme/footer.html:41
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Laatste update op %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:53
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Gebouwd met %(sphinx_web)s met een"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documentation
#: sphinx_rtd_theme/footer.html:55
msgid "theme"
msgstr "thema"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:57
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "geleverd door %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:97
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Zoek binnen %(docstitle)s"

#: sphinx_rtd_theme/layout.html:105
msgid "About these documents"
msgstr "Over deze documenten"

#: sphinx_rtd_theme/layout.html:108
msgid "Index"
msgstr "Index"

#: sphinx_rtd_theme/layout.html:111 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Zoek"

#: sphinx_rtd_theme/layout.html:114
msgid "Copyright"
msgstr "Copyright"

#: sphinx_rtd_theme/layout.html:143
msgid "Logo"
msgstr "Logo"

#. This is an ARIA section label for the main navigation menu
#: sphinx_rtd_theme/layout.html:166
msgid "Navigation menu"
msgstr "Navigatiemenu"

#. This is an ARIA section label for the navigation menu that is visible when
#. viewing the page on mobile devices
#: sphinx_rtd_theme/layout.html:188
msgid "Mobile navigation menu"
msgstr "Navigatiemenu voor mobiel"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr "Zet JavaScript aan om de zoekfunctie mogelijk te maken."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Zoekresultaten"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Zoekpoging vond geen documenten. Zorg ervoor dat alle woorden correct zijn "
"gespeld en dat voldoende categorieën zijn geselecteerd."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Zoek in documentatie"

#: sphinx_rtd_theme/versions.html:3 sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Versies"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Downloads"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "Op Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Project Home"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Bouwresultaten"
