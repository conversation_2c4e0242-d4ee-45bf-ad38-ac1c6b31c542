# English translations for sphinx_rtd_theme.
# Copyright (C) 2019 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2019.
# 
# Translators:
# <PERSON> <<EMAIL>>, 2020
# 
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 0.4.3.dev0\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-02-06 15:36+0100\n"
"PO-Revision-Date: 2019-07-16 21:44+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2020\n"
"Language-Team: Swedish (https://www.transifex.com/readthedocs/teams/101354/sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: sphinx_rtd_theme/breadcrumbs.html:37 sphinx_rtd_theme/breadcrumbs.html:39
msgid "Edit on GitHub"
msgstr "Editera på GitHub"

#: sphinx_rtd_theme/breadcrumbs.html:44 sphinx_rtd_theme/breadcrumbs.html:46
msgid "Edit on Bitbucket"
msgstr "Editera på Bitbucket"

#: sphinx_rtd_theme/breadcrumbs.html:51 sphinx_rtd_theme/breadcrumbs.html:53
msgid "Edit on GitLab"
msgstr "Editera på GitLab"

#: sphinx_rtd_theme/breadcrumbs.html:56 sphinx_rtd_theme/breadcrumbs.html:58
msgid "View page source"
msgstr "Visa sidkälla"

#: sphinx_rtd_theme/breadcrumbs.html:69 sphinx_rtd_theme/footer.html:6
msgid "Previous"
msgstr "Tillbaka"

#: sphinx_rtd_theme/breadcrumbs.html:72 sphinx_rtd_theme/footer.html:9
msgid "Next"
msgstr "Nästa"

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:30
msgid "Build"
msgstr "Bygg"

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:36
msgid "Revision"
msgstr "Ändra"

#: sphinx_rtd_theme/footer.html:41
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr "Senast uppdaterad %(last_updated)s."

#. the variable "sphinx_web" is a link to the Sphinx project documentation
#. with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:53
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr "Gjord med %(sphinx_web)s med hjälp av"

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documentation
#: sphinx_rtd_theme/footer.html:55
msgid "theme"
msgstr "tema"

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:57
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr "erhållet av %(readthedocs_web)s"

#: sphinx_rtd_theme/layout.html:97
#, python-format
msgid "Search within %(docstitle)s"
msgstr "Sök i %(docstitle)s"

#: sphinx_rtd_theme/layout.html:105
msgid "About these documents"
msgstr "Om dessa dokument"

#: sphinx_rtd_theme/layout.html:108
msgid "Index"
msgstr "Index"

#: sphinx_rtd_theme/layout.html:111 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr "Sök"

#: sphinx_rtd_theme/layout.html:114
msgid "Copyright"
msgstr "Upphovsrätt"

#: sphinx_rtd_theme/layout.html:143
msgid "Logo"
msgstr "Logo"

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr ""
"Var vänlig och aktivera JavaScript för att möjliggöra sökfunktionaliteten."

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr "Sökresultat"

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words are"
" spelled correctly and that you've selected enough categories."
msgstr ""
"Din sökning gav inga träffar. Var vänlig och se till att alla ord är rätt "
"stavade och att du har valt tillräckligt många kategorier."

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr "Sök i dokumentationen"

#: sphinx_rtd_theme/versions.html:3 sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr "Versioner"

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr "Nerladdningar"

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr "På Read the Docs"

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr "Projekt Hem"

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr "Versioner"
