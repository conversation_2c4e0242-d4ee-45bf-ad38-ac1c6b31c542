# Translations template for sphinx_rtd_theme.
# Copyright (C) 2023 ORGANIZATION
# This file is distributed under the same license as the sphinx_rtd_theme
# project.
# <AUTHOR> <EMAIL>, 2023.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: sphinx_rtd_theme 1.2.0rc4\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2023-02-06 15:36+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.11.0\n"

#. This is an ARIA section label for page links, including previous/next page
#. link and links to GitHub/GitLab/etc.
#: sphinx_rtd_theme/breadcrumbs.html:22
msgid "Page navigation"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:37 sphinx_rtd_theme/breadcrumbs.html:39
msgid "Edit on GitHub"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:44 sphinx_rtd_theme/breadcrumbs.html:46
msgid "Edit on Bitbucket"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:51 sphinx_rtd_theme/breadcrumbs.html:53
msgid "Edit on GitLab"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:56 sphinx_rtd_theme/breadcrumbs.html:58
msgid "View page source"
msgstr ""

#. This is an ARIA section label for sequential page links, such as previous
#. and next page links.
#: sphinx_rtd_theme/breadcrumbs.html:67
msgid "Sequential page navigation"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:69 sphinx_rtd_theme/footer.html:6
msgid "Previous"
msgstr ""

#: sphinx_rtd_theme/breadcrumbs.html:72 sphinx_rtd_theme/footer.html:9
msgid "Next"
msgstr ""

#. This is an ARIA section label for the footer section of the page.
#: sphinx_rtd_theme/footer.html:4
msgid "Footer"
msgstr ""

#: sphinx_rtd_theme/footer.html:21
#, python-format
msgid "&#169; <a href=\"%(path)s\">Copyright</a> %(copyright)s."
msgstr ""

#: sphinx_rtd_theme/footer.html:23
#, python-format
msgid "&#169; Copyright %(copyright)s."
msgstr ""

#. Build is a noun, not a verb
#: sphinx_rtd_theme/footer.html:30
msgid "Build"
msgstr ""

#. the phrase "revision" comes from Git, referring to a commit
#: sphinx_rtd_theme/footer.html:36
msgid "Revision"
msgstr ""

#: sphinx_rtd_theme/footer.html:41
#, python-format
msgid "Last updated on %(last_updated)s."
msgstr ""

#. the variable "sphinx_web" is a link to the Sphinx project documentation with
#. the text "Sphinx"
#: sphinx_rtd_theme/footer.html:53
#, python-format
msgid "Built with %(sphinx_web)s using a"
msgstr ""

#. "theme" refers to a theme for Sphinx, which alters the appearance of the
#. generated documentation
#: sphinx_rtd_theme/footer.html:55
msgid "theme"
msgstr ""

#. this is always used as "provided by Read the Docs", and should not imply
#. Read the Docs is an author of the generated documentation.
#: sphinx_rtd_theme/footer.html:57
#, python-format
msgid "provided by %(readthedocs_web)s"
msgstr ""

#: sphinx_rtd_theme/layout.html:97
#, python-format
msgid "Search within %(docstitle)s"
msgstr ""

#: sphinx_rtd_theme/layout.html:105
msgid "About these documents"
msgstr ""

#: sphinx_rtd_theme/layout.html:108
msgid "Index"
msgstr ""

#: sphinx_rtd_theme/layout.html:111 sphinx_rtd_theme/search.html:11
msgid "Search"
msgstr ""

#: sphinx_rtd_theme/layout.html:114
msgid "Copyright"
msgstr ""

#: sphinx_rtd_theme/layout.html:143
msgid "Logo"
msgstr ""

#. This is an ARIA section label for the main navigation menu
#: sphinx_rtd_theme/layout.html:166
msgid "Navigation menu"
msgstr ""

#. This is an ARIA section label for the navigation menu that is visible when
#. viewing the page on mobile devices
#: sphinx_rtd_theme/layout.html:188
msgid "Mobile navigation menu"
msgstr ""

#: sphinx_rtd_theme/search.html:31
msgid "Please activate JavaScript to enable the search functionality."
msgstr ""

#. Search is a noun, not a verb
#: sphinx_rtd_theme/search.html:39
msgid "Search Results"
msgstr ""

#: sphinx_rtd_theme/search.html:41
msgid ""
"Your search did not match any documents. Please make sure that all words "
"are spelled correctly and that you've selected enough categories."
msgstr ""

#: sphinx_rtd_theme/searchbox.html:4
msgid "Search docs"
msgstr ""

#: sphinx_rtd_theme/versions.html:3 sphinx_rtd_theme/versions.html:11
msgid "Versions"
msgstr ""

#: sphinx_rtd_theme/versions.html:17
msgid "Downloads"
msgstr ""

#. The phrase "Read the Docs" is not translated
#: sphinx_rtd_theme/versions.html:24
msgid "On Read the Docs"
msgstr ""

#: sphinx_rtd_theme/versions.html:26
msgid "Project Home"
msgstr ""

#: sphinx_rtd_theme/versions.html:29
msgid "Builds"
msgstr ""

