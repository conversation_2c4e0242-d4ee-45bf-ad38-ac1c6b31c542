#!/usr/bin/env python3
"""
Bulletin Field Validation Script

This script validates that all date and field replacements are working correctly
in the bulletin generation process. It can be used to ensure no fields are missed
when generating bulletins for different dates.
"""

import sys
import re
from pathlib import Path
from datetime import date, timed<PERSON>ta
from typing import List, Dict, Tuple

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bulletin_generator.core.database import BulletinDatabase
from bulletin_generator.core.content_generator import ContentGenerator


def check_date_consistency(content: str, bulletin_date: date) -> List[str]:
    """Check for date consistency issues in the content"""
    issues = []
    
    # Expected date formats
    expected_short = bulletin_date.strftime('%d-%b')  # e.g., "17-Aug"
    expected_full = bulletin_date.strftime('%d %B %Y')  # e.g., "17 August 2025"
    
    # Check for old date patterns that might not have been updated
    old_date_patterns = [
        r'\d{1,2}-[A-Za-z]{3}',  # DD-Mon format
        r'\d{1,2}\s+[A-Za-z]+\s+\d{4}',  # DD Month YYYY format
    ]
    
    for pattern in old_date_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            # Check if this is the expected date
            if match != expected_short and not match.startswith(expected_full[:2]):
                # This might be an old date that wasn't updated
                issues.append(f"Potential old date found: '{match}' (expected format like '{expected_short}')")
    
    # Check for year consistency
    current_year = str(bulletin_date.year)
    year_matches = re.findall(r'\b20\d{2}\b', content)
    for year in year_matches:
        if year != current_year and int(year) < bulletin_date.year:
            issues.append(f"Old year found: '{year}' (expected '{current_year}')")
    
    return issues


def check_service_times(content: str) -> List[str]:
    """Check that service times are present and consistent"""
    issues = []
    
    # Expected service times
    expected_times = ['7.45am', '9.00am', '10.45am', '1.00pm']
    
    for time in expected_times:
        if time not in content:
            issues.append(f"Service time missing: '{time}'")
    
    # Check for unexpected time formats
    time_patterns = re.findall(r'\d{1,2}[:.]\d{2}[ap]m', content)
    for time_match in time_patterns:
        if time_match not in expected_times:
            issues.append(f"Unexpected time format found: '{time_match}'")
    
    return issues


def check_people_fields(content: str) -> List[str]:
    """Check that people/contact fields are present"""
    issues = []
    
    # Check for minister name
    if "Rev. Jason Grimsey" not in content:
        issues.append("Minister name 'Rev. Jason Grimsey' not found")
    
    # Check for other common name patterns that might need updating
    name_patterns = re.findall(r'Rev\.\s+[A-Za-z\s]+', content)
    for name in name_patterns:
        if name.strip() != "Rev. Jason Grimsey":
            issues.append(f"Different minister name found: '{name}' (check if this should be updated)")
    
    return issues


def validate_bulletin_content(content: str, bulletin_date: date) -> Dict[str, List[str]]:
    """Validate all aspects of bulletin content"""
    validation_results = {
        'date_issues': check_date_consistency(content, bulletin_date),
        'time_issues': check_service_times(content),
        'people_issues': check_people_fields(content),
        'general_issues': []
    }
    
    # General checks
    if not content:
        validation_results['general_issues'].append("Content is empty")
    
    if len(content) < 1000:
        validation_results['general_issues'].append("Content seems too short (less than 1000 characters)")
    
    return validation_results


def test_bulletin_generation(test_date: date) -> Tuple[bool, Dict[str, List[str]]]:
    """Test bulletin generation for a specific date and validate the output"""
    try:
        print(f"🧪 Testing bulletin generation for {test_date.strftime('%A, %d %B %Y')}...")
        
        # Initialize components
        db = BulletinDatabase()
        generator = ContentGenerator(db)
        
        # Generate bulletin data
        bulletin_data = generator.generate_bulletin_data(test_date)
        
        # Generate the bulletin content
        generator.template_parser.load_template()
        modified_content = generator._apply_template_replacements(bulletin_data)
        
        if not modified_content:
            return False, {'general_issues': ['Failed to generate bulletin content']}
        
        # Validate the content
        validation_results = validate_bulletin_content(modified_content, test_date)
        
        # Check if there are any issues
        total_issues = sum(len(issues) for issues in validation_results.values())
        success = total_issues == 0
        
        return success, validation_results
        
    except Exception as e:
        return False, {'general_issues': [f'Exception during generation: {str(e)}']}


def print_validation_results(validation_results: Dict[str, List[str]], test_date: date):
    """Print validation results in a readable format"""
    total_issues = sum(len(issues) for issues in validation_results.values())
    
    if total_issues == 0:
        print(f"✅ All validations passed for {test_date.strftime('%d %B %Y')}")
        return
    
    print(f"⚠️ Found {total_issues} validation issues for {test_date.strftime('%d %B %Y')}:")
    
    for category, issues in validation_results.items():
        if issues:
            category_name = category.replace('_', ' ').title()
            print(f"\n  {category_name}:")
            for issue in issues:
                print(f"    • {issue}")


def main():
    """Main validation function"""
    print("🔍 Bulletin Field Validation Tool")
    print("=" * 50)
    
    # Test dates - current Sunday and next few Sundays
    test_dates = []
    
    # Find next Sunday
    today = date.today()
    days_until_sunday = (6 - today.weekday()) % 7
    if days_until_sunday == 0:
        days_until_sunday = 7  # If today is Sunday, get next Sunday
    
    next_sunday = today + timedelta(days=days_until_sunday)
    
    # Test next 3 Sundays
    for i in range(3):
        test_dates.append(next_sunday + timedelta(weeks=i))
    
    print(f"Testing bulletin generation for {len(test_dates)} dates...")
    
    all_passed = True
    results_summary = []
    
    for test_date in test_dates:
        success, validation_results = test_bulletin_generation(test_date)
        
        if not success:
            all_passed = False
        
        total_issues = sum(len(issues) for issues in validation_results.values())
        results_summary.append((test_date, success, total_issues))
        
        print_validation_results(validation_results, test_date)
        print()
    
    # Print summary
    print("=" * 50)
    print("📊 Validation Summary:")
    
    for test_date, success, issue_count in results_summary:
        status = "✅ PASS" if success else f"❌ FAIL ({issue_count} issues)"
        print(f"  {test_date.strftime('%d %B %Y')}: {status}")
    
    if all_passed:
        print("\n🎉 All bulletin validations passed!")
        print("The field mapping system is working correctly.")
        return 0
    else:
        print("\n⚠️ Some validations failed.")
        print("Please review the issues above and check your field mappings.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
