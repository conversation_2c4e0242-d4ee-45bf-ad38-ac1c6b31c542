-- Church Bulletin Database Schema
-- SQLite database for storing annual bulletin content

-- Service times and locations
CREATE TABLE service_times (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    location TEXT NOT NULL, -- 'Beachmere', 'Caboolture', 'Upper Caboolture', 'Tongan Service'
    time TEXT NOT NULL, -- '7.45am', '9.00am', '10.45am', '1.00pm'
    preacher TEXT, -- 'Rev<PERSON>', etc.
    service_type TEXT DEFAULT 'Regular', -- 'Regular', 'Combined', 'Special'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bible verses and readings
CREATE TABLE bible_verses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    verse_reference TEXT NOT NULL, -- 'Matthew 5:1-12', etc.
    verse_text TEXT,
    theme TEXT, -- Optional theme for the week
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Church events and announcements
CREATE TABLE events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL, -- Date the event appears in bulletin
    event_date DATE, -- Actual date of the event (can be different)
    title TEXT NOT NULL,
    description TEXT,
    location TEXT,
    time TEXT,
    contact_person TEXT,
    contact_details TEXT,
    category TEXT, -- 'Service', 'Social', 'Meeting', 'Special', 'Ongoing'
    priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Weekly diary entries and ongoing content
CREATE TABLE diary_entries (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    day_of_week TEXT, -- 'Monday', 'Tuesday', etc.
    time TEXT,
    activity TEXT NOT NULL,
    location TEXT,
    contact_person TEXT,
    recurring BOOLEAN DEFAULT FALSE, -- For weekly recurring events
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bulletin metadata
CREATE TABLE bulletin_metadata (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL UNIQUE,
    title TEXT, -- Main bulletin title
    edition_number INTEGER,
    theme TEXT, -- Weekly theme
    special_notes TEXT, -- Any special instructions or notes
    template_version TEXT DEFAULT 'standard',
    status TEXT DEFAULT 'draft', -- 'draft', 'review', 'published'
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Preaching topics and sermon series
CREATE TABLE preaching_topics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    topic TEXT NOT NULL,
    series_name TEXT, -- e.g., 'Lamentations', 'Parables'
    scripture_reference TEXT,
    description TEXT,
    preacher TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Contact information and ministry details
CREATE TABLE contacts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    role TEXT, -- 'Minister', 'Elder', 'Secretary', etc.
    phone TEXT,
    email TEXT,
    availability_notes TEXT, -- e.g., 'Not available Wednesdays or Saturdays'
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Template placeholders mapping
CREATE TABLE template_fields (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    field_name TEXT NOT NULL UNIQUE, -- e.g., 'service_date', 'beachmere_time', etc.
    field_type TEXT NOT NULL, -- 'date', 'text', 'time', 'contact'
    description TEXT,
    default_value TEXT,
    required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_service_times_date ON service_times(date);
CREATE INDEX IF NOT EXISTS idx_bible_verses_date ON bible_verses(date);
CREATE INDEX IF NOT EXISTS idx_events_date ON events(date);
CREATE INDEX IF NOT EXISTS idx_events_event_date ON events(event_date);
CREATE INDEX IF NOT EXISTS idx_diary_entries_date ON diary_entries(date);
CREATE INDEX IF NOT EXISTS idx_bulletin_metadata_date ON bulletin_metadata(date);
CREATE INDEX IF NOT EXISTS idx_preaching_topics_date ON preaching_topics(date);

-- Sample data for testing
INSERT OR IGNORE INTO contacts (name, role, phone, email, availability_notes) VALUES
('Rev. Jason Grimsey', 'Minister', '0412 542 697', '<EMAIL>', 'Not available on Wednesdays or Saturdays'),
('Church Office', 'Administration', '', '<EMAIL>', '');

INSERT OR IGNORE INTO template_fields (field_name, field_type, description, required) VALUES
('bulletin_date', 'date', 'Main bulletin date', TRUE),
('beachmere_time', 'time', 'Beachmere service time', TRUE),
('caboolture_time', 'time', 'Caboolture service time', TRUE),
('upper_caboolture_time', 'time', 'Upper Caboolture service time', TRUE),
('tongan_service_time', 'time', 'Tongan service time', FALSE),
('preacher_name', 'text', 'Name of the preacher', TRUE),
('bible_verse', 'text', 'Weekly Bible verse', FALSE),
('sermon_topic', 'text', 'Sermon topic or series', FALSE);
