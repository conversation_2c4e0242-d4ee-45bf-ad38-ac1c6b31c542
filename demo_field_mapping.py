#!/usr/bin/env python3
"""
Demo script showing how to use the new field mapping system

This script demonstrates the key features of the enhanced field mapping system
and shows how to easily manage template field replacements.
"""

import sys
from pathlib import Path
from datetime import date

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

def demo_field_detection():
    """Demonstrate field detection capabilities"""
    print("🔍 DEMO: Field Detection")
    print("-" * 40)
    
    try:
        from bulletin_generator.core.template_parser import TemplateParser
        
        # Initialize template parser
        parser = TemplateParser()
        parser.load_template()
        
        # Detect all fields
        detected_fields = parser.detect_all_fields()
        
        print("✅ Template loaded and analyzed")
        print(f"📄 Template: {parser.template_path}")
        print("\n📋 Detected Fields by Category:")
        
        for category, fields in detected_fields.items():
            if fields:
                print(f"\n  {category.upper()}:")
                for field in fields[:5]:  # Show first 5
                    print(f"    • {field}")
                if len(fields) > 5:
                    print(f"    ... and {len(fields) - 5} more")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def demo_field_mapping():
    """Demonstrate field mapping generation"""
    print("\n\n📅 DEMO: Field Mapping Generation")
    print("-" * 40)
    
    try:
        from bulletin_generator.core.field_mapper import FieldMapper
        from datetime import date
        
        # Initialize field mapper
        mapper = FieldMapper()
        
        # Test with a specific date
        test_date = date(2025, 8, 17)  # Sunday, August 17, 2025
        
        # Generate date mappings
        date_mappings = mapper.get_date_mappings(test_date)
        
        print(f"✅ Generated mappings for {test_date.strftime('%A, %d %B %Y')}")
        print(f"📊 Total date mappings: {len(date_mappings)}")
        
        print("\n🔄 Sample Date Replacements:")
        for old_text, new_text in list(date_mappings.items())[:5]:
            print(f"  '{old_text}' → '{new_text}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def demo_configuration_management():
    """Demonstrate configuration file management"""
    print("\n\n⚙️ DEMO: Configuration Management")
    print("-" * 40)
    
    try:
        from bulletin_generator.utils.field_config_manager import FieldConfigManager
        
        # Initialize config manager
        config_manager = FieldConfigManager()
        
        # Show current categories
        categories = config_manager.list_categories()
        print(f"✅ Configuration loaded")
        print(f"📂 Available categories: {len(categories)}")
        
        for category in categories:
            fields = config_manager.list_fields_in_category(category)
            print(f"  {category}: {len(fields)} fields")
        
        # Validate configuration
        is_valid, errors = config_manager.validate_config()
        print(f"\n✅ Configuration validation: {'VALID' if is_valid else 'INVALID'}")
        if errors:
            print("⚠️ Validation errors:")
            for error in errors[:3]:
                print(f"  • {error}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def demo_bulletin_generation():
    """Demonstrate bulletin generation with new field mapping"""
    print("\n\n📄 DEMO: Bulletin Generation")
    print("-" * 40)
    
    try:
        from bulletin_generator.core.database import BulletinDatabase
        from bulletin_generator.core.content_generator import ContentGenerator
        
        # Initialize components
        db = BulletinDatabase()
        generator = ContentGenerator(db)
        
        # Test date
        test_date = date(2025, 8, 17)
        
        print(f"✅ Generating bulletin for {test_date.strftime('%A, %d %B %Y')}")
        
        # Generate bulletin data
        bulletin_data = generator.generate_bulletin_data(test_date)
        print(f"📊 Bulletin data generated")
        print(f"  Services: {len(bulletin_data.service_times)}")
        print(f"  Events: {len(bulletin_data.events)}")
        
        # Load template and apply replacements
        generator.template_parser.load_template()
        original_length = len(generator.template_parser.template_content)
        
        modified_content = generator._apply_template_replacements(bulletin_data)
        modified_length = len(modified_content)
        
        print(f"✅ Template processing completed")
        print(f"📄 Original template: {original_length:,} characters")
        print(f"📄 Modified template: {modified_length:,} characters")
        
        # Check for some expected replacements
        if "17-Aug" in modified_content:
            print("✅ Date replacement successful (found '17-Aug')")
        else:
            print("⚠️ Date replacement may not have worked")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all demonstrations"""
    print("🎯 Field Mapping System Demonstration")
    print("=" * 50)
    print("This demo shows the key features of the enhanced field mapping system.")
    print("=" * 50)
    
    demos = [
        demo_field_detection,
        demo_field_mapping,
        demo_configuration_management,
        demo_bulletin_generation
    ]
    
    success_count = 0
    
    for demo in demos:
        try:
            if demo():
                success_count += 1
        except Exception as e:
            print(f"❌ Demo failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Demo Results: {success_count}/{len(demos)} successful")
    
    if success_count == len(demos):
        print("🎉 All demos completed successfully!")
        print("\n💡 Next Steps:")
        print("1. Run: python main.py fields analyze")
        print("2. Run: python main.py fields auto-update")
        print("3. Run: python main.py generate --date 2025-08-17")
    else:
        print("⚠️ Some demos failed. Check the error messages above.")
    
    print("\n📖 For detailed usage instructions, see FIELD_MAPPING_GUIDE.md")


if __name__ == '__main__':
    main()
