# Church Bulletin Generator

An automated system for generating weekly church bulletins from templates and database content.

## Features

- **Automated Content Generation**: Automatically populate bulletins with date-based content
- **Database Management**: Store annual data including Bible verses, events, service times, and contact information
- **Template System**: Use SVG templates that preserve exact formatting and design
- **Multiple Output Formats**: Generate PDF, SVG, and PNG formats
- **Command Line Interface**: Easy-to-use CLI for all operations
- **Future Web Interface**: Designed with JavaScript web interface integration in mind

## Installation

1. **Clone or download the project**
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Run setup**:
   ```bash
   python setup.py
   ```
4. **Initialize database**:
   ```bash
   python main.py init-db
   ```

## Quick Start

### Check System Status
```bash
python main.py status
```

### Generate a Bulletin
```bash
# Generate for today
python main.py generate

# Generate for specific date
python main.py generate --date 2025-08-10

# Generate in different format
python main.py generate --date 2025-08-10 --format pdf
```

### Manage Events
```bash
# Add an event
python main.py add-event --date 2025-08-10 --title "Special Service" --time "7:00pm"

# List events
python main.py list-events --date 2025-08-10
```

## Project Structure

```
bulletin_generator/
├── core/
│   ├── database.py          # Database operations
│   ├── template_parser.py   # SVG template parsing
│   └── content_generator.py # Content generation engine
├── models/
│   ├── bulletin.py          # Bulletin data model
│   ├── service.py           # Service time model
│   ├── event.py             # Event model
│   ├── bible_verse.py       # Bible verse model
│   └── contact.py           # Contact model
├── utils/
│   ├── date_utils.py        # Date utilities
│   ├── file_utils.py        # File utilities
│   └── validation.py        # Validation functions
└── config.py                # Configuration settings

templates/                   # SVG template files
output/                     # Generated bulletin files
data/                       # Database files
```

## Database Schema

The system uses SQLite with the following main tables:

- **service_times**: Service schedules and preachers
- **events**: Church events and announcements  
- **bible_verses**: Weekly Bible verses and themes
- **contacts**: Ministry team contact information
- **bulletin_metadata**: Bulletin titles and metadata

## Template System

The system uses SVG templates that preserve exact formatting. The template parser:

- Identifies text elements that can be replaced
- Maps content to specific locations and times
- Maintains professional design and layout
- Supports multiple output formats

## Configuration

Key settings in `bulletin_generator/config.py`:

- **Database path**: Location of SQLite database
- **Template directory**: SVG template files
- **Output directory**: Generated bulletin files
- **Service locations**: Default church locations and times
- **Contact information**: Default ministry contacts

## Future Development

This system is designed to integrate with a JavaScript web interface for:

- User-friendly data entry forms
- Visual bulletin preview
- Drag-and-drop template editing
- Online bulletin distribution

## Support

For technical support or questions about the system, contact the development team or refer to the project documentation.

## License

This project is developed for church use and may be adapted for similar organizations.
