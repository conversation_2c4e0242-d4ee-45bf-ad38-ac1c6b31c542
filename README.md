# Church Bulletin Generator

An automated system for generating weekly church bulletins from templates and database content.

## Features

- **Automated Content Generation**: Automatically populate bulletins with date-based content
- **Database Management**: Store annual data including Bible verses, events, service times, and contact information
- **Template System**: Use SVG templates that preserve exact formatting and design
- **Multiple Output Formats**: Generate PDF, SVG, and PNG formats
- **Command Line Interface**: Easy-to-use CLI for all operations
- **Future Web Interface**: Designed with JavaScript web interface integration in mind

## Prerequisites

- Python 3.8 or higher
- pip (Python package manager)
- Git (for cloning the repository)

## Installation

### Step 1: Clone the Repository

```bash
git clone https://github.com/fjord-an/cruca-bulletin.git
cd cruca-bulletin
```

### Step 2: (Optional) Python Version Management

If you need to manage multiple Python versions, you can use a version manager:

#### macOS (using pyenv)

```bash
# Install pyenv via Homebrew
brew install pyenv

# Add pyenv to your shell
echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.zshrc
echo 'command -v pyenv >/dev/null || export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.zshrc
echo 'eval "$(pyenv init -)"' >> ~/.zshrc

# Restart your shell
source ~/.zshrc

# Install Python 3.11 (recommended)
pyenv install 3.11.9
pyenv local 3.11.9
```

#### Windows (using pyenv-win)

```powershell
# Install pyenv-win via PowerShell
Invoke-WebRequest -UseBasicParsing -Uri "https://raw.githubusercontent.com/pyenv-win/pyenv-win/master/pyenv-win/install-pyenv-win.ps1" -OutFile "./install-pyenv-win.ps1"; .\install-pyenv-win.ps1

# Restart PowerShell and install Python
pyenv install 3.11.9
pyenv local 3.11.9
```

Alternatively, for Windows users, you can use the official Python installer from [python.org](https://www.python.org/downloads/).

### Step 3: Create Virtual Environment

It's recommended to use a virtual environment to isolate project dependencies:

#### macOS/Linux

```bash
# Create virtual environment named 'cruca-bulletin'
python3 -m venv cruca-bulletin

# Activate the virtual environment
source cruca-bulletin/bin/activate
```

#### Windows

```powershell
# Create virtual environment named 'cruca-bulletin'
python -m venv cruca-bulletin

# Activate the virtual environment
# For Command Prompt:
cruca-bulletin\Scripts\activate.bat

# For PowerShell:
cruca-bulletin\Scripts\Activate.ps1
```

### Step 4: Install Dependencies

With your virtual environment activated:

```bash
# Upgrade pip to the latest version
pip install --upgrade pip

# Install project dependencies
pip install -r requirements.txt
```

### Step 5: Run Setup

```bash
python setup.py
```

### Step 6: Initialize Database

```bash
python main.py init-db
```

## Managing the Virtual Environment

### Deactivating the Virtual Environment

When you're done working on the project:

```bash
deactivate
```

### Reactivating the Virtual Environment

When you return to work on the project:

#### macOS/Linux
```bash
source cruca-bulletin/bin/activate
```

#### Windows
```powershell
# Command Prompt:
cruca-bulletin\Scripts\activate.bat

# PowerShell:
cruca-bulletin\Scripts\Activate.ps1
```

### Updating Dependencies

To update all packages to their latest compatible versions:

```bash
pip install --upgrade -r requirements.txt
```

### Freezing Dependencies

If you add new packages to the project:

```bash
pip freeze > requirements.txt
```

## Quick Start

### Check System Status
```bash
python main.py status
```

### Generate a Bulletin
```bash
# Generate for today
python main.py generate

# Generate for specific date
python main.py generate --date 2025-08-10

# Generate in different format
python main.py generate --date 2025-08-10 --format pdf
```

### Manage Events
```bash
# Add an event
python main.py add-event --date 2025-08-10 --title "Special Service" --time "7:00pm"

# List events
python main.py list-events --date 2025-08-10
```

## License

This project is developed for church use and may be adapted for similar organizations.

## Project Structure

```
bulletin_generator/
├── core/
│   ├── database.py          # Database operations
│   ├── template_parser.py   # SVG template parsing
│   └── content_generator.py # Content generation engine
├── models/
│   ├── bulletin.py          # Bulletin data model
│   ├── service.py           # Service time model
│   ├── event.py             # Event model
│   ├── bible_verse.py       # Bible verse model
│   └── contact.py           # Contact model
├── utils/
│   ├── date_utils.py        # Date utilities
│   ├── file_utils.py        # File utilities
│   └── validation.py        # Validation functions
└── config.py                # Configuration settings

templates/                   # SVG template files
output/                     # Generated bulletin files
data/                       # Database files
```

## Database Schema

The system uses SQLite with the following main tables:

- **service_times**: Service schedules and preachers
- **events**: Church events and announcements  
- **bible_verses**: Weekly Bible verses and themes
- **contacts**: Ministry team contact information
- **bulletin_metadata**: Bulletin titles and metadata

## Template System

The system uses SVG templates that preserve exact formatting. The template parser:

- Identifies text elements that can be replaced
- Maps content to specific locations and times
- Maintains professional design and layout
- Supports multiple output formats

## Configuration

Key settings in `bulletin_generator/config.py`:

- **Database path**: Location of SQLite database
- **Template directory**: SVG template files
- **Output directory**: Generated bulletin files
- **Service locations**: Default church locations and times
- **Contact information**: Default ministry contacts

## Future Development

This system is designed to integrate with a JavaScript web interface for:

- User-friendly data entry forms
- Visual bulletin preview
- Drag-and-drop template editing
- Online bulletin distribution

## Support

For technical support or questions about the system, contact the development team or refer to the project documentation.

## License

This project is developed for church use and may be adapted for similar organizations.
