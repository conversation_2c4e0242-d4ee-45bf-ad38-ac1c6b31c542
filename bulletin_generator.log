2025-08-07 19:54:52,740 - bulletin_generator.core.database - INFO - Creating new database at /Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/data/bulletin.db
2025-08-07 19:54:52,748 - bulletin_generator.core.database - INFO - Database initialized successfully
2025-08-07 19:54:52,749 - bulletin_generator.core.database - ERROR - Error initializing database: table service_times already exists
2025-08-07 19:55:26,341 - bulletin_generator.core.database - ERROR - Error initializing database: index idx_service_times_date already exists
2025-08-07 19:55:49,121 - bulletin_generator.core.database - ERROR - Error initializing database: UNIQUE constraint failed: template_fields.field_name
2025-08-07 20:00:09,238 - bulletin_generator.core.database - INFO - Database initialized successfully
2025-08-07 20:00:38,167 - bulletin_generator.core.content_generator - INFO - Generating bulletin data for 2025-08-10
2025-08-07 20:00:38,176 - bulletin_generator.core.content_generator - INFO - Generating bulletin in svg format
2025-08-07 20:00:38,177 - bulletin_generator.core.template_parser - INFO - Template loaded from /Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/templates/Bulletin 030825.svg
2025-08-07 20:00:38,186 - bulletin_generator.core.template_parser - INFO - Template saved to /Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/output/bulletin_20250810.svg
2025-08-07 20:00:38,186 - bulletin_generator.core.content_generator - INFO - Bulletin generated: /Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/output/bulletin_20250810.svg
2025-08-07 20:01:49,889 - bulletin_generator.core.content_generator - INFO - Generating bulletin data for 2025-08-10
2025-08-07 20:01:49,895 - bulletin_generator.core.content_generator - INFO - Generating bulletin in pdf format
2025-08-07 20:01:49,897 - bulletin_generator.core.template_parser - INFO - Template loaded from /Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/templates/Bulletin 030825.svg
2025-08-07 20:01:50,501 - bulletin_generator.core.content_generator - ERROR - Error converting to PDF: no library called "cairo-2" was found
no library called "cairo" was found
no library called "libcairo-2" was found
cannot load library 'libcairo.so.2': dlopen(libcairo.so.2, 0x0002): tried: 'libcairo.so.2' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.so.2' (no such file), '/usr/lib/libcairo.so.2' (no such file, not in dyld cache), 'libcairo.so.2' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.so.2'
cannot load library 'libcairo.2.dylib': dlopen(libcairo.2.dylib, 0x0002): tried: 'libcairo.2.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.2.dylib' (no such file), '/usr/lib/libcairo.2.dylib' (no such file, not in dyld cache), 'libcairo.2.dylib' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.2.dylib'
cannot load library 'libcairo-2.dll': dlopen(libcairo-2.dll, 0x0002): tried: 'libcairo-2.dll' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo-2.dll' (no such file), '/usr/lib/libcairo-2.dll' (no such file, not in dyld cache), 'libcairo-2.dll' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo-2.dll'
2025-08-07 20:01:50,504 - root - ERROR - Error generating bulletin: no library called "cairo-2" was found
no library called "cairo" was found
no library called "libcairo-2" was found
cannot load library 'libcairo.so.2': dlopen(libcairo.so.2, 0x0002): tried: 'libcairo.so.2' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.so.2' (no such file), '/usr/lib/libcairo.so.2' (no such file, not in dyld cache), 'libcairo.so.2' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.so.2'
cannot load library 'libcairo.2.dylib': dlopen(libcairo.2.dylib, 0x0002): tried: 'libcairo.2.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.2.dylib' (no such file), '/usr/lib/libcairo.2.dylib' (no such file, not in dyld cache), 'libcairo.2.dylib' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.2.dylib'
cannot load library 'libcairo-2.dll': dlopen(libcairo-2.dll, 0x0002): tried: 'libcairo-2.dll' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo-2.dll' (no such file), '/usr/lib/libcairo-2.dll' (no such file, not in dyld cache), 'libcairo-2.dll' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo-2.dll'
Traceback (most recent call last):
  File "/Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/main.py", line 83, in generate
    output_path = generator.generate_bulletin(bulletin_data, output_format=format, output_path=output)
  File "/Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/bulletin_generator/core/content_generator.py", line 126, in generate_bulletin
    output_path = self._convert_to_pdf(modified_content, output_path)
  File "/Users/<USER>/Documents/CRUCA/Bulletin/Template-Bulletin/bulletin_generator/core/content_generator.py", line 175, in _convert_to_pdf
    import cairosvg
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cairosvg/__init__.py", line 25, in <module>
    from . import surface  # noqa isort:skip
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cairosvg/surface.py", line 9, in <module>
    import cairocffi as cairo
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cairocffi/__init__.py", line 60, in <module>
    cairo = dlopen(
  File "/Users/<USER>/Library/Python/3.9/lib/python/site-packages/cairocffi/__init__.py", line 57, in dlopen
    raise OSError(error_message)  # pragma: no cover
OSError: no library called "cairo-2" was found
no library called "cairo" was found
no library called "libcairo-2" was found
cannot load library 'libcairo.so.2': dlopen(libcairo.so.2, 0x0002): tried: 'libcairo.so.2' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.so.2' (no such file), '/usr/lib/libcairo.so.2' (no such file, not in dyld cache), 'libcairo.so.2' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.so.2'
cannot load library 'libcairo.2.dylib': dlopen(libcairo.2.dylib, 0x0002): tried: 'libcairo.2.dylib' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo.2.dylib' (no such file), '/usr/lib/libcairo.2.dylib' (no such file, not in dyld cache), 'libcairo.2.dylib' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo.2.dylib'
cannot load library 'libcairo-2.dll': dlopen(libcairo-2.dll, 0x0002): tried: 'libcairo-2.dll' (no such file), '/System/Volumes/Preboot/Cryptexes/OSlibcairo-2.dll' (no such file), '/usr/lib/libcairo-2.dll' (no such file, not in dyld cache), 'libcairo-2.dll' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'libcairo-2.dll'
