#!/usr/bin/env python3
"""
Test script for field mapping system

This script tests the field mapping functionality to ensure all fields
are being detected and updated correctly.
"""

import sys
from pathlib import Path
from datetime import date, datetime
import json

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bulletin_generator.core.database import BulletinDatabase
from bulletin_generator.core.content_generator import ContentGenerator
from bulletin_generator.core.field_mapper import FieldMapper
from bulletin_generator.utils.field_config_manager import FieldConfigManager


def test_field_detection():
    """Test field detection in template"""
    print("🔍 Testing field detection...")
    
    try:
        db = BulletinDatabase()
        generator = ContentGenerator(db)
        
        # Analyze template fields
        analysis = generator.analyze_template_fields()
        
        print(f"✓ Template analyzed: {analysis['template_path']}")
        print(f"✓ Detected fields by category:")
        
        total_detected = 0
        for category, fields in analysis['detected_fields'].items():
            print(f"  {category}: {len(fields)} fields")
            if fields:
                for field in fields[:3]:  # Show first 3 fields
                    print(f"    - {field}")
                if len(fields) > 3:
                    print(f"    ... and {len(fields) - 3} more")
            total_detected += len(fields)
        
        print(f"✓ Total detected fields: {total_detected}")
        
        # Check field comparison
        comparison = analysis['field_comparison']
        print(f"✓ Field mapping status:")
        print(f"  Mapped: {len(comparison['mapped'])}")
        print(f"  Unmapped: {len(comparison['unmapped'])}")
        print(f"  Missing: {len(comparison['missing'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Field detection test failed: {e}")
        return False


def test_field_mapping_generation():
    """Test field mapping generation for a specific date"""
    print("\n📅 Testing field mapping generation...")
    
    try:
        # Test with a specific bulletin date
        test_date = date(2025, 8, 17)  # Sunday, August 17, 2025
        
        field_mapper = FieldMapper()
        
        # Test date mappings
        date_mappings = field_mapper.get_date_mappings(test_date)
        print(f"✓ Generated {len(date_mappings)} date mappings for {test_date}")
        
        # Show some examples
        for old_text, new_text in list(date_mappings.items())[:5]:
            print(f"  '{old_text}' → '{new_text}'")
        
        # Test with mock service times
        from bulletin_generator.models.service import ServiceTime
        mock_services = [
            ServiceTime(location="Beachmere", time="7.45am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Caboolture", time="9.00am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Upper Caboolture", time="10.45am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Tongan Service", time="1.00pm", preacher="Rev. Jason Grimsey")
        ]
        
        time_mappings = field_mapper.get_service_time_mappings(mock_services)
        print(f"✓ Generated {len(time_mappings)} time mappings")
        
        # Test with mock contacts
        from bulletin_generator.models.contact import Contact
        mock_minister = Contact(name="Rev. Jason Grimsey", role="Minister", 
                               phone="0412 542 697", email="<EMAIL>")
        
        contacts = {'minister': mock_minister}
        people_mappings = field_mapper.get_people_mappings(contacts)
        print(f"✓ Generated {len(people_mappings)} people mappings")
        
        # Test all mappings together
        all_mappings = field_mapper.get_all_mappings(test_date, mock_services, contacts)
        print(f"✓ Total mappings generated: {len(all_mappings)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Field mapping generation test failed: {e}")
        return False


def test_config_management():
    """Test configuration file management"""
    print("\n⚙️ Testing configuration management...")
    
    try:
        # Test with temporary config file
        test_config_path = Path("test_field_mappings.json")
        
        config_manager = FieldConfigManager(test_config_path)
        
        # Test adding a field mapping
        config_manager.add_field_mapping(
            "test_fields",
            "Test Field",
            {
                "type": "test",
                "description": "Test field for validation"
            }
        )
        
        # Test saving and loading
        config_manager.save_config()
        print("✓ Configuration saved successfully")
        
        # Load it back
        new_manager = FieldConfigManager(test_config_path)
        loaded_config = new_manager.load_config()
        print("✓ Configuration loaded successfully")
        
        # Validate the test field was saved
        test_field = new_manager.get_field_mapping("test_fields", "Test Field")
        if test_field and test_field["type"] == "test":
            print("✓ Field mapping saved and loaded correctly")
        else:
            print("✗ Field mapping not saved correctly")
            return False
        
        # Test validation
        is_valid, errors = config_manager.validate_config()
        print(f"✓ Configuration validation: {'Valid' if is_valid else 'Invalid'}")
        if errors:
            print(f"  Validation errors: {len(errors)}")
        
        # Clean up test file
        if test_config_path.exists():
            test_config_path.unlink()
            print("✓ Test configuration file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration management test failed: {e}")
        return False


def test_template_replacement():
    """Test actual template replacement"""
    print("\n🔄 Testing template replacement...")
    
    try:
        db = BulletinDatabase()
        generator = ContentGenerator(db)
        
        # Load template
        generator.template_parser.load_template()
        original_content = generator.template_parser.template_content
        
        if not original_content:
            print("✗ Could not load template content")
            return False
        
        print(f"✓ Template loaded ({len(original_content)} characters)")
        
        # Test with a specific date
        test_date = date(2025, 8, 17)
        
        # Create mock bulletin data
        from bulletin_generator.models.bulletin import BulletinData
        from bulletin_generator.models.service import ServiceTime
        from bulletin_generator.models.contact import Contact
        
        mock_services = [
            ServiceTime(location="Beachmere", time="7.45am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Caboolture", time="9.00am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Upper Caboolture", time="10.45am", preacher="Rev. Jason Grimsey"),
            ServiceTime(location="Tongan Service", time="1.00pm", preacher="Rev. Jason Grimsey")
        ]
        
        mock_minister = Contact(name="Rev. Jason Grimsey", role="Minister")
        
        bulletin_data = BulletinData(
            date=test_date,
            title=f"Weekly Bulletin - {test_date.strftime('%d %B %Y')}",
            service_times=mock_services,
            minister=mock_minister
        )
        
        # Apply replacements
        modified_content = generator._apply_template_replacements(bulletin_data)
        
        if modified_content and modified_content != original_content:
            print("✓ Template replacement applied successfully")
            
            # Count differences
            changes = 0
            if "17-Aug" in modified_content and "10-Aug" not in modified_content:
                changes += 1
                print("  ✓ Date fields updated")
            
            print(f"✓ Template modified ({len(modified_content)} characters)")
            return True
        else:
            print("✗ Template replacement did not modify content")
            return False
        
    except Exception as e:
        print(f"✗ Template replacement test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🧪 Field Mapping System Test Suite")
    print("=" * 50)
    
    tests = [
        test_field_detection,
        test_field_mapping_generation,
        test_config_management,
        test_template_replacement
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            print("❌ Test failed!")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Field mapping system is working correctly.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the output above.")
        return 1


if __name__ == '__main__':
    sys.exit(main())
